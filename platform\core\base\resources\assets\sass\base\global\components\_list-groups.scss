/***
Customized List Group
***/

/* Contextual variants */
.list-group {
    > li:first-child {
        border-radius-topleft: $general-border-radius;
        border-radius-topright: $general-border-radius;
    }

    > li:last-child {
        border-radius-bottomleft: $general-border-radius;
        border-radius-bottomright: $general-border-radius;
    }
}

@mixin list-group-item-variant($state, $background, $color) {
    .list-group-item-#{$state} {
        color: $color;
        background-color: $background;

        &.list-group-item-action {
            &:hover,
            &:focus {
                color: $color;
                background-color: shade-color($background, 10%);
            }

            &.active {
                color: $white;
                background-color: $color;
                border-color: $color;
            }
        }
    }
}

.list-group {
    @include list-group-item-variant(success, $state-success-bg, $state-success-text);
}

.list-group {
    @include list-group-item-variant(info, $state-info-bg, $state-info-text);
}

.list-group {
    @include list-group-item-variant(warning, $state-warning-bg, $state-warning-text);
}

.list-group {
    @include list-group-item-variant(danger, $state-danger-bg, $state-danger-text);
}
