(()=>{"use strict";function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(e,t){for(var o=0;o<t.length;o++){var a=t[o];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,n(a.key),a)}}function n(t){var n=function(t,n){if("object"!=e(t)||!t)return t;var o=t[Symbol.toPrimitive];if(void 0!==o){var a=o.call(t,n||"default");if("object"!=e(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(t)}(t,"string");return"symbol"==e(n)?n:String(n)}var o,a=function(){function o(e){var t,a,i;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),t=this,i={oldestFirst:!0,text:"Toastify is awesome!",node:void 0,duration:3e3,selector:void 0,callback:function(){},close:!1,gravity:"toastify-top",position:"",className:"",stopOnFocus:!0,onClick:function(){},offset:{x:0,y:0},escapeMarkup:!0,ariaLive:"polite",style:{background:""}},(a=n(a="defaults"))in t?Object.defineProperty(t,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[a]=i,this.options={},this.toastElement=null,this._rootElement=document.body,this._init(e)}var a,i,r;return a=o,(i=[{key:"showToast",value:function(){var e=this;if(this.toastElement=this._buildToast(),"string"==typeof this.options.selector?this._rootElement=document.getElementById(this.options.selector):this.options.selector instanceof HTMLElement||this.options.selector instanceof ShadowRoot?this._rootElement=this.options.selector:this._rootElement=document.body,!this._rootElement)throw"Root element is not defined";return this._rootElement.insertBefore(this.toastElement,this._rootElement.firstChild),this._reposition(),this.options.duration>0&&(this.toastElement.timeOutValue=window.setTimeout((function(){e._removeElement(e.toastElement)}),this.options.duration)),this}},{key:"hideToast",value:function(){this.toastElement.timeOutValue&&clearTimeout(this.toastElement.timeOutValue),this._removeElement(this.toastElement)}},{key:"_init",value:function(e){this.options=Object.assign(this.defaults,e),this.toastElement=null,this.options.gravity="bottom"===e.gravity?"toastify-bottom":"toastify-top",this.options.stopOnFocus=void 0===e.stopOnFocus||e.stopOnFocus}},{key:"_buildToast",value:function(){var t=this;if(!this.options)throw"Toastify is not initialized";var n=document.createElement("div");for(var o in n.className="toastify on ".concat(this.options.className," pe-5"),n.className+=" toastify-".concat(this.options.position),n.className+=" ".concat(this.options.gravity),this.options.style)n.style[o]=this.options.style[o];if(this.options.ariaLive&&n.setAttribute("aria-live",this.options.ariaLive),""!==this.options.icon){var a=document.createElement("div");a.className="toastify-icon",a.innerHTML=this.options.icon,n.appendChild(a)}var i=document.createElement("span");if(i.className="toastify-text",this.options.node&&this.options.node.nodeType===Node.ELEMENT_NODE?i.appendChild(this.options.node):this.options.escapeMarkup?i.innerText=this.options.text:i.innerHTML=this.options.text,n.appendChild(i),!0===this.options.close){var r=document.createElement("button");r.type="button",r.setAttribute("aria-label","Close"),r.className="toast-close",r.style.cssText="position: absolute; top: 8px; inset-inline-end: 8px;",r.innerHTML='<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                <path d="M18 6l-12 12"></path>\n                <path d="M6 6l12 12"></path>\n            </svg>',r.addEventListener("click",(function(e){e.stopPropagation(),t._removeElement(t.toastElement),window.clearTimeout(t.toastElement.timeOutValue)}));var l=window.innerWidth>0?window.innerWidth:screen.width;"left"===this.options.position&&l>360?n.insertAdjacentElement("afterbegin",r):n.appendChild(r)}if(this.options.stopOnFocus&&this.options.duration>0&&(n.addEventListener("mouseover",(function(e){window.clearTimeout(n.timeOutValue)})),n.addEventListener("mouseleave",(function(){n.timeOutValue=window.setTimeout((function(){t._removeElement(n)}),t.options.duration)}))),"function"==typeof this.options.onClick&&n.addEventListener("click",(function(e){e.stopPropagation(),t.options.onClick()})),"object"===e(this.options.offset)){var c=this._getAxisOffsetAValue("x",this.options),s=this._getAxisOffsetAValue("y",this.options),d="left"===this.options.position?c:"-".concat(c),u="toastify-top"===this.options.gravity?s:"-".concat(s);n.style.transform="translate(".concat(d,",").concat(u,")")}return n}},{key:"_removeElement",value:function(e){var t=this;e.className=e.className.replace(" on",""),window.setTimeout((function(){t.options.node&&t.options.node.parentNode&&t.options.node.parentNode.removeChild(t.options.node),e.parentNode&&e.parentNode.removeChild(e),t.options.callback.call(e),t._reposition()}),400)}},{key:"_reposition",value:function(){for(var e,t={top:15,bottom:15},n={top:15,bottom:15},o={top:15,bottom:15},a=this._rootElement.querySelectorAll(".toastify"),i=0;i<a.length;i++){e=!0===a[i].classList.contains("toastify-top")?"toastify-top":"toastify-bottom";var r=a[i].offsetHeight;e=e.substr(9,e.length-1),(window.innerWidth>0?window.innerWidth:screen.width)<=360?(a[i].style[e]="".concat(o[e],"px"),o[e]+=r+15):!0===a[i].classList.contains("toastify-left")?(a[i].style[e]="".concat(t[e],"px"),t[e]+=r+15):(a[i].style[e]="".concat(n[e],"px"),n[e]+=r+15)}}},{key:"_getAxisOffsetAValue",value:function(e,t){return t.offset[e]?isNaN(t.offset[e])?t.offset[e]:"".concat(t.offset[e],"px"):"0px"}}])&&t(a.prototype,i),r&&t(a,r),Object.defineProperty(a,"prototype",{writable:!1}),o}();(o=document.createElement("style")).textContent="\n        .toastify {\n            padding: 0.75rem 2rem 0.75rem 0.75rem;\n            color: #ffffff;\n            display: flex;\n            align-items: center;\n            gap: 0.5rem;\n            box-shadow:\n                0 3px 6px -1px rgba(0, 0, 0, 0.12),\n                0 10px 36px -4px rgba(77, 96, 232, 0.3);\n            background: -webkit-linear-gradient(315deg, #73a5ff, #5477f5);\n            background: linear-gradient(135deg, #73a5ff, #5477f5);\n            position: fixed;\n            opacity: 0;\n            transition: all 0.4s cubic-bezier(0.215, 0.61, 0.355, 1);\n            border-radius: 2px;\n            cursor: pointer;\n            text-decoration: none;\n            z-index: 999999;\n            width: 25rem;\n            max-width: calc(100% - 30px);\n        }\n\n        .toastify.on {\n            opacity: 1;\n        }\n\n        .toastify-icon {\n            width: 1.5rem;\n            height: 1.5rem;\n        }\n\n        .toast-close {\n            background: transparent;\n            border: 0;\n            color: white;\n            cursor: pointer;\n            font-family: inherit;\n            font-size: 1em;\n            opacity: 0.4;\n            padding: 0 5px;\n            position: absolute;\n            top: 0.25rem;\n            inset-inline-end: 0.25rem;\n        }\n\n        .toast-close svg {\n            width: 1em;\n            height: 1em;\n        }\n\n        .toastify-text a {\n            text-decoration: underline;\n            color: #fff;\n        }\n\n        .toastify-right {\n            inset-inline-end: 15px;\n        }\n\n        .toastify-left {\n            inset-inline-start: 15px;\n        }\n\n        .toastify-top {\n            top: -150px;\n        }\n\n        .toastify-bottom {\n            bottom: -150px;\n        }\n\n        .toastify-rounded {\n            border-radius: 25px;\n        }\n\n        .toastify-center {\n            margin-inline-start: auto;\n            margin-inline-end: auto;\n            inset-inline-start: 0;\n            inset-inline-end: 0;\n            max-width: fit-content;\n            max-width: -moz-fit-content;\n        }\n\n        @media only screen and (max-width: 360px) {\n            .toastify-right,\n            .toastify-left {\n                margin-inline-start: auto;\n                margin-inline-end: auto;\n                inset-inline-start: 0;\n                inset-inline-end: 0;\n                max-width: fit-content;\n            }\n        }\n    ",document.head.appendChild(o);const i=function(e){return new a(e)};function r(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function l(e){return l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},l(e)}function c(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */c=function(){return t};var e,t={},n=Object.prototype,o=n.hasOwnProperty,a=Object.defineProperty||function(e,t,n){e[t]=n.value},i="function"==typeof Symbol?Symbol:{},r=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",d=i.toStringTag||"@@toStringTag";function u(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,n){return e[t]=n}}function p(e,t,n,o){var i=t&&t.prototype instanceof y?t:y,r=Object.create(i.prototype),l=new M(o||[]);return a(r,"_invoke",{value:O(e,n,l)}),r}function f(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=p;var h="suspendedStart",m="suspendedYield",g="executing",v="completed",b={};function y(){}function w(){}function k(){}var $={};u($,r,(function(){return this}));var x=Object.getPrototypeOf,C=x&&x(x(N([])));C&&C!==n&&o.call(C,r)&&($=C);var E=k.prototype=y.prototype=Object.create($);function _(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function T(e,t){function n(a,i,r,c){var s=f(e[a],e,i);if("throw"!==s.type){var d=s.arg,u=d.value;return u&&"object"==l(u)&&o.call(u,"__await")?t.resolve(u.__await).then((function(e){n("next",e,r,c)}),(function(e){n("throw",e,r,c)})):t.resolve(u).then((function(e){d.value=e,r(d)}),(function(e){return n("throw",e,r,c)}))}c(s.arg)}var i;a(this,"_invoke",{value:function(e,o){function a(){return new t((function(t,a){n(e,o,t,a)}))}return i=i?i.then(a,a):a()}})}function O(t,n,o){var a=h;return function(i,r){if(a===g)throw new Error("Generator is already running");if(a===v){if("throw"===i)throw r;return{value:e,done:!0}}for(o.method=i,o.arg=r;;){var l=o.delegate;if(l){var c=S(l,o);if(c){if(c===b)continue;return c}}if("next"===o.method)o.sent=o._sent=o.arg;else if("throw"===o.method){if(a===h)throw a=v,o.arg;o.dispatchException(o.arg)}else"return"===o.method&&o.abrupt("return",o.arg);a=g;var s=f(t,n,o);if("normal"===s.type){if(a=o.done?v:m,s.arg===b)continue;return{value:s.arg,done:o.done}}"throw"===s.type&&(a=v,o.method="throw",o.arg=s.arg)}}}function S(t,n){var o=n.method,a=t.iterator[o];if(a===e)return n.delegate=null,"throw"===o&&t.iterator.return&&(n.method="return",n.arg=e,S(t,n),"throw"===n.method)||"return"!==o&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+o+"' method")),b;var i=f(a,t.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,b;var r=i.arg;return r?r.done?(n[t.resultName]=r.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,b):r:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,b)}function j(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function L(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function M(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(j,this),this.reset(!0)}function N(t){if(t||""===t){var n=t[r];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,i=function n(){for(;++a<t.length;)if(o.call(t,a))return n.value=t[a],n.done=!1,n;return n.value=e,n.done=!0,n};return i.next=i}}throw new TypeError(l(t)+" is not iterable")}return w.prototype=k,a(E,"constructor",{value:k,configurable:!0}),a(k,"constructor",{value:w,configurable:!0}),w.displayName=u(k,d,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===w||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,k):(e.__proto__=k,u(e,d,"GeneratorFunction")),e.prototype=Object.create(E),e},t.awrap=function(e){return{__await:e}},_(T.prototype),u(T.prototype,s,(function(){return this})),t.AsyncIterator=T,t.async=function(e,n,o,a,i){void 0===i&&(i=Promise);var r=new T(p(e,n,o,a),i);return t.isGeneratorFunction(n)?r:r.next().then((function(e){return e.done?e.value:r.next()}))},_(E),u(E,d,"Generator"),u(E,r,(function(){return this})),u(E,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var o in t)n.push(o);return n.reverse(),function e(){for(;n.length;){var o=n.pop();if(o in t)return e.value=o,e.done=!1,e}return e.done=!0,e}},t.values=N,M.prototype={constructor:M,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(L),!t)for(var n in this)"t"===n.charAt(0)&&o.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function a(o,a){return l.type="throw",l.arg=t,n.next=o,a&&(n.method="next",n.arg=e),!!a}for(var i=this.tryEntries.length-1;i>=0;--i){var r=this.tryEntries[i],l=r.completion;if("root"===r.tryLoc)return a("end");if(r.tryLoc<=this.prev){var c=o.call(r,"catchLoc"),s=o.call(r,"finallyLoc");if(c&&s){if(this.prev<r.catchLoc)return a(r.catchLoc,!0);if(this.prev<r.finallyLoc)return a(r.finallyLoc)}else if(c){if(this.prev<r.catchLoc)return a(r.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<r.finallyLoc)return a(r.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var a=this.tryEntries[n];if(a.tryLoc<=this.prev&&o.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var r=i?i.completion:{};return r.type=e,r.arg=t,i?(this.method="next",this.next=i.finallyLoc,b):this.complete(r)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),b},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),L(n),b}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var a=o.arg;L(n)}return a}}throw new Error("illegal catch attempt")},delegateYield:function(t,n,o){return this.delegate={iterator:N(t),resultName:n,nextLoc:o},"next"===this.method&&(this.arg=e),b}},t}function s(e,t,n,o,a,i,r){try{var l=e[i](r),c=l.value}catch(e){return void n(e)}l.done?t(c):Promise.resolve(c).then(o,a)}function d(e){return function(){var t=this,n=arguments;return new Promise((function(o,a){var i=e.apply(t,n);function r(e){s(i,o,a,r,l,"next",e)}function l(e){s(i,o,a,r,l,"throw",e)}r(void 0)}))}}function u(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,f(o.key),o)}}function p(e,t,n){return(t=f(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function f(e){var t=function(e,t){if("object"!=l(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=l(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==l(t)?t:String(t)}var h=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.initGlobalModal(),this.countCharacter(),this.manageSidebar(),this.handleWayPoint(),this.handleTurnOffDebugMode(),e.initResources(),e.initGlobalResources(),e.handleCounterUp(),e.initMediaIntegrate(),BotbleVariables&&"0"===BotbleVariables.authorized&&"undefined"!=typeof route&&this.processAuthorize(),this.countMenuItemNotifications()}var t,n,o,a;return t=e,n=[{key:"initGlobalModal",value:function(){$((function(){$('[data-bb-toggle="modal"]').on("click",(function(e){e.preventDefault();var t=$(e.currentTarget),n=t.data("type"),o=t.data("actionModal"),a=JSON.parse(t.data("payload")),i=t.data("url"),r=t.data("method"),l=t.data("confirmText"),c=t.data("cancelText"),s="global-".concat(n,"-modal"),d=$("#"+s),u=t.find(".modal-replace-title").html(),p=t.find(".modal-replace-description").html();if(d.find(".mb-2 i").siblings().remove(),d.find(".mb-2").append(u),d.find(".mb-2").append(p),o){var f="\n                    <div class='modal-footer'>\n                        <div class='w-100'>\n                            <div class='row'>\n                                <div class='col'>\n                                    <button type='button' class='w-100 btn' data-bs-dismiss='modal'>".concat(c,"</button>\n                                </div>\n                                <div class='col'>\n                                    <button type='button' class='w-100 btn btn-").concat(n," confirm-trigger-single-action-button'>").concat(l,"</button>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n                    ");d.find(".modal-body").siblings().remove(),d.find(".modal-body").after(f),d.find(".confirm-trigger-single-action-button").on("click",(function(){$.ajax({type:r,url:i,data:a,success:function(e){},error:function(e){},complete:function(){return d.modal("hide")}})}))}else d.find(".modal-footer").remove();d.modal("show")}))}))}},{key:"countCharacter",value:function(){$.fn.charCounter=function(e,t){var n,o;e=e||100,t=$.extend({container:"<span></span>",classname:"charcounter",format:"(%1/%2)",pulse:!0,delay:0,allowOverLimit:!1},t);var a=function(a,r){var l=(a=$(a)).val().length,c=e-a.val().length;r.html(t.format.replace(/%1/,l).replace(/%2/,e)),r.toggleClass("text-danger",a.val().length>e),!t.allowOverLimit&&a.val().length>e&&(a.val(a.val().substring(0,e)),t.pulse&&!n&&i(r,!0)),t.delay>0&&(o&&window.clearTimeout(o),o=window.setTimeout((function(){r.html(t.format.replace(/%1/,c).replace(/%2/,e))}),t.delay))},i=function e(t,o){n&&(window.clearTimeout(n),n=null),t.animate({opacity:.1},100,(function(){$(t).animate({opacity:1},100)})),o&&(n=window.setTimeout((function(){return e(t)}),200))};return this.each((function(e,n){var o;t.container.match(/^<.+>$/)?($(n).nextAll("."+t.classname).remove(),o=$(t.container).insertAfter(n).addClass(t.classname)):o=$(t.container),$(n).off(".charCounter").on("keydown.charCounter",(function(){a(n,o)})).on("keypress.charCounter",(function(){a(n,o)})).on("keyup.charCounter",(function(){a(n,o)})).on("focus.charCounter",(function(){a(n,o)})).on("mouseover.charCounter",(function(){a(n,o)})).on("mouseout.charCounter",(function(){a(n,o)})).on("paste.charCounter",(function(){setTimeout((function(){a(n,o)}),10)})),n.addEventListener&&n.addEventListener("input",(function(){a(n,o)}),!1),a(n,o)}))},$(document).on("click","input[data-counter], textarea[data-counter]",(function(e){var t=$(e.currentTarget);$(e.currentTarget).charCounter(t.data("counter"),{container:"<small></small>",allowOverLimit:""==t.data("allow-over-limit")})}))}},{key:"manageSidebar",value:function(){var e=$("body"),t=$(".navigation"),n=$(".sidebar-content");t.find("li.active").parents("li").addClass("active"),t.find("li").has("ul").children("a").parent("li").addClass("has-ul"),$(document).on("click",".sidebar-toggle.d-none",(function(o){o.preventDefault(),e.toggleClass("sidebar-narrow"),e.toggleClass("page-sidebar-closed"),e.hasClass("sidebar-narrow")?(t.children("li").children("ul").css("display",""),n.delay().queue((function(){$(o.currentTarget).show().addClass("animated fadeIn").clearQueue()}))):(t.children("li").children("ul").hide(),t.children("li.active").children("ul").show(),n.delay().queue((function(){$(o.currentTarget).show().addClass("animated fadeIn").clearQueue()})))}))}},{key:"handleWayPoint",value:function(){$(document).find("[data-bb-waypoint]").each((function(){var e=$($(this).data("bb-target"));new Waypoint({element:$(this),handler:function(t){"down"===t?e.show():e.hide()}})}))}},{key:"handleTurnOffDebugMode",value:function(){var t=$(document).find("#debug-mode-turn-off-confirmation-modal");if(t.length){var n=t.find("#debug-mode-turn-off-form-submit");n.length&&n.on("click",(function(o){o.preventDefault(),e.showButtonLoading(n[0]),$httpClient.make().post(n.data("url")).then((function(n){var o=n.data;e.showSuccess(o.message),t.modal("hide"),setTimeout((function(){window.location.reload()}),1e3)})).finally((function(){e.hideButtonLoading(n[0])}))}))}}},{key:"processAuthorize",value:function(){$httpClient.makeWithoutErrorHandler().post(route("membership.authorize")).catch((function(){}))}},{key:"countMenuItemNotifications",value:function(){$(".menu-item-count").length&&$httpClient.make().get(route("menu-items-count")).then((function(e){e.data.data.map((function(e){e.value>0&&$(".menu-item-count.".concat(e.key)).text(e.value).show().removeClass("hidden")}))}))}}],o=[{key:"initCoreIcon",value:function(){var t=$(document).find("[data-bb-core-icon]"),n=function(e){var t=e.id,n=e.text;return void 0===t&&(t=""),$('<span><span class="dropdown-item-indicator">'.concat(n,"</span> ").concat(t,"</span>"))};e.select(t,{ajax:{url:t.data("url"),delay:250,cache:!0,data:function(e){return{q:e.term,page:e.page||1}},processResults:function(e){return{results:$.map(e.data,(function(e,t){return{text:e,id:t}})),pagination:{more:e.next_page_url&&Object.keys(e.data).length>0}}}},placeholder:t.data("placeholder"),templateResult:n,templateSelection:n})}},{key:"blockUI",value:function(t){t=t||{},e.showLoading(t.target)}},{key:"unblockUI",value:function(t){e.hideLoading(t)}},{key:"showNotice",value:function(t,n){var o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",a="notices_msg.".concat(t,".").concat(n),r="",l="";e.noticesTimeout[a]&&clearTimeout(e.noticesTimeout[a]),e.noticesTimeout[a]=setTimeout((function(){if(!o)switch(t){case"error":o=BotbleVariables.languages.notices_msg.error;break;case"success":o=BotbleVariables.languages.notices_msg.success}switch(t){case"error":r="#f44336",l='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0" /><path d="M12 9v4" /><path d="M12 16v.01" /></svg>';break;case"success":r="#4caf50",l='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M5 12l5 5l10 -10" /></svg>'}i({text:n,duration:5e3,close:!0,gravity:"bottom",position:"right",stopOnFocus:!0,escapeMarkup:!1,icon:l,style:{background:r}}).showToast()}),e.noticesTimeoutCount)}},{key:"showError",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";this.showNotice("error",e,t)}},{key:"showSuccess",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";this.showNotice("success",e,t)}},{key:"handleError",value:function(t){void 0===t.errors||_.isArray(t.errors)?void 0!==t.responseJSON?void 0!==t.responseJSON.errors?422===t.status&&e.handleValidationError(t.responseJSON.errors):void 0!==t.responseJSON.message?e.showError(t.responseJSON.message):$.each(t.responseJSON,(function(t,n){$.each(n,(function(t,n){e.showError(n)}))})):e.showError(t.statusText):e.handleValidationError(t.errors)}},{key:"handleValidationError",value:function(t){var n="";$.each(t,(function(e,t){n+=t+"\n"})),e.showError(n)}},{key:"callScroll",value:function(e){e.mCustomScrollbar({theme:"dark",scrollInertia:0,callbacks:{whileScrolling:function(){e.find(".tableFloatingHeaderOriginal").css({top:-this.mcs.top+"px"})}}}),e.stickyTableHeaders({scrollableArea:e,fixedOffset:2})}},{key:"copyToClipboard",value:(a=d(c().mark((function t(n){return c().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!navigator.clipboard||!window.isSecureContext){t.next=5;break}return t.next=3,navigator.clipboard.writeText(n);case 3:t.next=6;break;case 5:e.unsecuredCopyToClipboard(n);case 6:case"end":return t.stop()}}),t)}))),function(e){return a.apply(this,arguments)})},{key:"unsecuredCopyToClipboard",value:function(e){var t=document.createElement("textarea");t.value=e,t.style.position="absolute",t.style.left="-999999px",document.body.prepend(t),t.focus(),t.select();try{document.execCommand("copy")}catch(e){console.error("Unable to copy to clipboard",e)}document.body.removeChild(t)}},{key:"initDatePicker",value:function(e){if(jQuery().flatpickr){var t=$(document).find(e).find("input").data("date-format");t||(t="Y-m-d");var n=window.siteEditorLocale;"vi"===n&&(n="vn"),$(document).find(e).flatpickr({dateFormat:t,wrap:!0,locale:n||"en"})}}},{key:"initResources",value:function(){$.each($(document).find("select.select-search-full"),(function(t,n){e.select(n,{allowClear:$(n).data("allow-clear")})})),$.each($(document).find("select.select-full"),(function(t,n){e.select(n,{controlInput:null})})),$(document).find("select.select-autocomplete").each((function(t,n){e.select(n,{minimumInputLength:$(n).data("minimum-input")||1,width:"100%",delay:250,ajax:{url:$(n).data("url"),data:function(e){return{q:e.term,page:e.page||1}},dataType:"json",type:$(n).data("type")||"GET",processResults:function(e){return{results:$.map(e.data,(function(e){return Object.assign({text:e.name,id:e.id},e)})),pagination:{more:e.links?e.links.next:null}}},cache:!0}})})),$.each($(document).find(".select-multiple"),(function(t,n){var o=$(n);e.select(n,{allowClear:o.data("allow-clear"),placeholder:o.data("placeholder")}),$(this).hasClass(".select-sorting")&&$(this).on("select2:select",(function(e){var t=$(e.params.data.element);t.detach(),$(this).append(t),$(this).trigger("change")}))})),$.each($(document).find(".select-search-ajax"),(function(t,n){if($(n).data("url")){var o={placeholder:$(n).data("placeholder")||"--Select--",minimumInputLength:$(n).data("minimum-input")||1,width:"100%",delay:250,ajax:{url:$(n).data("url"),dataType:"json",type:$(n).data("type")||"GET",quietMillis:50,data:function(e){return{search:e.term,page:e.page||1}},processResults:function(e){var t=Array.isArray(e.data)?e.data:e.data.data;return{results:$.map(t,(function(e){return{text:e.name,id:e.id}})),pagination:{more:e.links?e.links.next:null}}},cache:!0},allowClear:!0};e.select(n,o)}})),$(document).find('[data-bb-toggle="google-font-selector"]').each((function(t,n){if(!$(n).hasClass("select2-hidden-accessible")){var o={templateResult:function(e){return e.id?$("<span style=\"font-family:'"+e.id+"';\"> "+e.text+"</span>"):e.text},width:"100%"};e.select(n,o)}})),jQuery().timepicker&&($(".timepicker-default").timepicker({autoclose:!0,showSeconds:!1,minuteStep:1,defaultTime:!1}),$(".timepicker-24").timepicker({autoclose:!0,minuteStep:5,showSeconds:!1,showMeridian:!1,defaultTime:!1,icons:{up:"icon ti ti-chevron-up",down:"icon ti ti-chevron-down"}})),jQuery().inputmask&&$.each($(document).find(".input-mask-number"),(function(e,t){var n,o,a,i;$(t).inputmask({alias:"numeric",rightAlign:!1,digits:null!==(n=$(t).data("digits"))&&void 0!==n?n:5,groupSeparator:null!==(o=$(t).data("thousands-separator"))&&void 0!==o?o:",",radixPoint:null!==(a=$(t).data("decimal-separator"))&&void 0!==a?a:".",digitsOptional:!0,placeholder:null!==(i=$(t).data("placeholder"))&&void 0!==i?i:"0",autoGroup:!0,autoUnmask:!0,removeMaskOnSubmit:!0})})),jQuery().colorpicker&&$.each($(document).find(".color-picker"),(function(e,t){$(t).colorpicker({popover:!1,inline:!1,container:!0,format:"hex",extensions:[{name:"swatches",options:{colors:{tetrad1:"#000000",tetrad2:"#000000",tetrad3:"#000000",tetrad4:"#000000"},namesAsValues:!1}}]}).on("colorpickerChange colorpickerCreate",(function(e){e.color.generate("tetrad").forEach((function(t,n){var o=t.string();e.colorpicker.picker.find('.colorpicker-swatch[data-name="tetrad'+(n+1)+'"]').attr("data-value",o).attr("title",o).find("> i").css("background-color",o)}))}))})),jQuery().fancybox&&($(".iframe-btn").fancybox({width:"900px",height:"700px",type:"iframe",autoScale:!1,openEffect:"none",closeEffect:"none",overlayShow:!0,overlayOpacity:.7}),$(".fancybox").fancybox({openEffect:"none",closeEffect:"none",overlayShow:!0,overlayOpacity:.7,helpers:{media:{}}})),jQuery().tooltip&&$('[data-bs-toggle="tooltip"]').tooltip({placement:"top",boundary:"window"}),jQuery().areYouSure&&$("form.dirty-check").areYouSure(),e.initDatePicker(".datepicker"),jQuery().textareaAutoSize&&$("textarea.textarea-auto-height").textareaAutoSize(),e.initCodeEditorComponent(),e.initColorPicker(),e.initLightbox(),e.initTreeCategoriesSelect(),e.initCoreIcon(),document.dispatchEvent(new CustomEvent("core-init-resources"))}},{key:"initGlobalResources",value:function(){$(document).on("submit",".js-base-form",(function(e){$(e.currentTarget).find("button[type=submit]").addClass("disabled")})),$(document).on("change",".media-image-input",(function(){var e=this;if(e.files&&e.files.length>0){var t=new FileReader;t.onload=function(t){$(e).closest(".image-box").find(".preview-image").prop("src",t.target.result)},t.readAsDataURL(e.files[0])}})),$(document).on("click",".media-select-file",(function(e){e.preventDefault(),e.stopPropagation(),$(this).closest(".attachment-wrapper").find(".media-file-input").trigger("click")})),e.initFieldCollapse(),e.initTreeCheckboxes(),e.initClipboard(),e.initDropdownCheckboxes()}},{key:"numberFormat",value:function(e,t,n,o){var a=isFinite(+e)?+e:0,i=isFinite(+t)?Math.abs(t):0,r=void 0===o?",":o,l=void 0===n?".":n,c=(i?function(e,t){var n=Math.pow(10,t);return Math.round(e*n)/n}(a,i):Math.round(a)).toString().split(".");return c[0].length>3&&(c[0]=c[0].replace(/\B(?=(?:\d{3})+(?!\d))/g,r)),(c[1]||"").length<i&&(c[1]=c[1]||"",c[1]+=new Array(i-c[1].length+1).join("0")),c.join(l)}},{key:"handleCounterUp",value:function(){$().counterUp&&$('[data-counter="counterup"]').counterUp({delay:10,time:1e3})}},{key:"openMediaUsing",value:function(e){}},{key:"handleOpenMedia",value:function(e){}},{key:"initMediaIntegrate",value:function(){if(jQuery().rvMedia){e.gallerySelectImageTemplate="\n            <div class='custom-image-box image-box'>\n                <input type='hidden' name='__name__' value='' class='image-data'>\n                    <div class='preview-image-wrapper w-100'>\n                    <div class='preview-image-inner'>\n                        <img src='".concat(RV_MEDIA_CONFIG.default_image,"' alt='").concat(RV_MEDIA_CONFIG.translations.preview_image,'\' class=\'preview-image\'>\n                        <div class=\'image-picker-backdrop\'></div>\n                        <span class=\'image-picker-remove-button\'>\n                            <button data-bb-toggle=\'image-picker-remove\' class=\'btn btn-sm btn-icon\'>\n                                <span class="icon-tabler-wrapper icon-sm icon-left">\n                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-x" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                                  <path stroke="none" d="M0 0h24v24H0z" fill="none"/>\n                                  <path d="M18 6l-12 12" />\n                                  <path d="M6 6l12 12" />\n                                </svg>\n                            </button>\n                        </span>\n                        <div data-bb-toggle=\'image-picker-edit\' class=\'image-box-actions cursor-pointer\'></div>\n                    </div>\n                </div>\n            </div>');var t=$(".btn_gallery");t.length>0&&t.each((function(){var e=$(this);$(e).rvMedia({multiple:!1,filter:"select-image"===$(e).data("action")?"image":"everything",view_in:"all_media",onSelectFiles:function(e,t){switch(t.data("action")){case"media-insert-ckeditor":var n="";$.each(e,(function(e,t){var o=t.full_url;if("youtube"===t.type)o=o.replace("watch?v=","embed/"),n+='<iframe width="420" height="315" src="'+o+'" frameborder="0" allowfullscreen loading="lazy"></iframe><br />';else if("image"===t.type){var a=t.alt?t.alt:t.name;n+='<img src="'+o+'" alt="'+a+'" loading="lazy"/><br />'}else n+='<a href="'+o+'">'+t.name+"</a><br />"})),window.EDITOR.CKEDITOR[t.data("result")].insertHtml(n);break;case"media-insert-tinymce":var o="";$.each(e,(function(e,t){var n=t.full_url;if("youtube"===t.type)n=n.replace("watch?v=","embed/"),o+="<iframe width='420' height='315' src='".concat(n,"' allowfullscreen loading='lazy'></iframe><br />");else if("image"===t.type){var a=t.alt?t.alt:t.name;o+="<img src='".concat(n,"' alt='").concat(a,"' loading='lazy'/><br />")}else o+="<a href='".concat(n,"'>").concat(t.name,"</a><br />")})),tinymce.activeEditor.execCommand("mceInsertContent",!1,o);break;case"select-image":var a=_.first(e),i=t.closest(".image-box"),r=t.data("allow-thumb");i.find(".image-data").val(a.url).trigger("change"),i.find(".preview-image").attr("src",r&&a.thumb?a.thumb:a.full_url),i.find('[data-bb-toggle="image-picker-remove"]').show(),i.find(".preview-image").removeClass("default-image"),i.find(".preview-image-wrapper").show();break;case"attachment":var l=_.first(e),c=t.closest(".attachment-wrapper");c.find(".attachment-url").val(l.url),c.find(".attachment-info").html('\n                                        <a href="'.concat(l.full_url,'" target="_blank" title="').concat(l.name,'">').concat(l.url,'</a>\n                                        <small class="d-block">').concat(l.size,"</small>\n                                    ")),c.find('[data-bb-toggle="media-file-remove"]').show(),c.find(".attachment-details").removeClass("hidden");break;default:var s=new CustomEvent("core-insert-media",{detail:{files:e,element:t}});document.dispatchEvent(s)}}})}));var n=function(t,n){var o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],a=e.gallerySelectImageTemplate,i=n.data("allow-thumb");_.forEach(t,(function(e,t){if(!_.includes(o,t)){var r=a.replace(/__name__/gi,n.data("name")),l=$('<div class="col-lg-2 col-md-3 col-4 gallery-image-item-handler mb-2">'+r+"</div>");l.find(".image-data").val(e.url).trigger("change"),l.find(".preview-image").attr("src",i?e.thumb:e.full_url).show(),i||l.find(".preview-image-wrapper").addClass("preview-image-wrapper-not-allow-thumb"),n.append(l),$(".list-images").find(".footer-action").show()}}))};new RvMediaStandAlone('[data-bb-toggle="gallery-add"]',{filter:"image",view_in:"all_media",onSelectFiles:function(e,t){var o=t.closest(".gallery-images-wrapper").find(".images-wrapper .list-gallery-media-images");o.removeClass("hidden"),$(".default-placeholder-gallery-image").addClass("hidden"),n(e,o)}}),new RvMediaStandAlone('[data-bb-toggle="image-picker-edit"]',{filter:"image",view_in:"all_media",onSelectFiles:function(e,t){var o=_.first(e),a=t.closest(".gallery-image-item-handler").find(".image-box"),i=t.closest(".list-gallery-media-images"),r=i.data("allow-thumb");a.find(".image-data").val(o.url).trigger("change"),a.find(".preview-image").attr("src",r?o.thumb:o.full_url).show(),n(e,i,[0])}}),$.each($(document).find('[data-bb-toggle="image-picker-choose"][data-target="popup"]'),(function(e,t){$(t).rvMedia({multiple:!1,filter:"image",view_in:"all_media",onSelectFiles:function(e,t){var n=_.first(e),o=t.closest(".image-box"),a=t.data("allow-thumb");o.find(".image-data").val(n.url).trigger("change"),o.find(".preview-image").attr("src",a&&n.thumb?n.thumb:n.full_url),o.find('[data-bb-toggle="image-picker-remove"]').show(),o.find(".preview-image").removeClass("default-image"),o.find(".preview-image-wrapper").show();var i=new CustomEvent("core-insert-media",{detail:{files:e,element:t}});document.dispatchEvent(i)}})}))}$(document).on("click",'[data-bb-toggle="image-picker-choose"][data-target="direct"]',(function(e){e.preventDefault(),e.stopPropagation(),$(e.currentTarget).closest(".image-box").find(".media-image-input").trigger("click")})),$(document).on("show.bs.modal","#image-picker-add-from-url",(function(e){var t=$(e.relatedTarget).data("bb-target");$(e.currentTarget).find('input[name="image-box-target"]').val(t)})),$(document).on("submit","#image-picker-add-from-url-form",(function(e){e.preventDefault();var t=$(e.currentTarget),n=t.closest(".modal"),o=n.find('button[type="submit"]');$httpClient.make().withButtonLoading(o).post(t.prop("action"),{url:t.find('input[name="url"]').val(),folderId:0}).then((function(e){var o=e.data;t[0].reset(),n.modal("hide");var a=$(t.find('input[name="image-box-target"]').val());a.find(".image-data").val(o.data.url).trigger("change"),a.find(".preview-image").prop("src",o.data.src),a.find('[data-bb-toggle="image-picker-remove"]').show(),a.find(".preview-image").removeClass("default-image"),a.find(".preview-image-wrapper").show()}))})),$(document).on("click",'[data-bb-toggle="image-picker-remove"]',(function(e){e.preventDefault();var t=$(e.currentTarget),n=t.closest(".image-box");n.find(".preview-image-wrapper img").prop("src",n.find(".preview-image-wrapper img").data("default")),n.find(".image-data").val("").trigger("change"),n.find(".preview-image").addClass("default-image"),t.hide()})),$(document).on("click",'[data-bb-toggle="media-file-remove"]',(function(e){e.preventDefault();var t=$(e.currentTarget),n=t.closest(".attachment-wrapper");n.find(".attachment-details").addClass("hidden"),n.find(".attachment-url").val(""),t.hide()})),$(document).on("click",'[data-bb-toggle="image-picker-remove"]',(function(e){e.preventDefault();var t=$(e.currentTarget);t.tooltip("dispose");var n=t.closest(".list-gallery-media-images");if(t.closest(".gallery-image-item-handler").remove(),0===n.find(".gallery-image-item-handler").length){var o=n.closest(".list-images");o.find(".default-placeholder-gallery-image").removeClass("hidden"),o.find(".footer-action").hide()}}));var o=$(".list-images");o.length&&($(document).on("click",'[data-bb-toggle="gallery-reset"]',(function(e){e.preventDefault(),o.find(".list-gallery-media-images .gallery-image-item-handler").remove(),o.find(".default-placeholder-gallery-image").removeClass("hidden"),o.find(".footer-action").hide()})),o.find(".list-gallery-media-images").each((function(e,t){if(jQuery().sortable){var n=$(t);n.data("ui-sortable")&&n.sortable("destroy"),n.sortable()}})))}},{key:"getViewPort",value:function(){var e=window,t="inner";return"innerWidth"in window||(t="client",e=document.documentElement||document.body),{width:e[t+"Width"],height:e[t+"Height"]}}},{key:"initCodeEditor",value:function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"css",o="object"===l(t),a=o?$(t):$(document).find("#"+t);t=o?t.id:t,(o?void 0!==a:a.length)&&(a.wrap("<div id='wrapper_".concat(t,"'><div class='container_content_codemirror'></div> </div>")),$("#wrapper_".concat(t)).append("<div class='handle-tool-drag' id='tool-drag_".concat(t,"'></div>")),CodeMirror.fromTextArea(a[0],{extraKeys:{"Ctrl-Space":"autocomplete"},lineNumbers:!0,mode:n,autoRefresh:!0,lineWrapping:!0}),$(".handle-tool-drag").mousedown((function(t){var n=$(t.currentTarget);n.attr("data-start_h",n.parent().find(".CodeMirror").height()).attr("data-start_y",t.pageY),$("body").attr("data-dragtool",n.attr("id")).on("mousemove",e.onDragTool),$(window).on("mouseup",e.onReleaseTool)})))}},{key:"onDragTool",value:function(e){var t=$("#".concat($("body").attr("data-dragtool"))),n=parseInt(t.attr("data-start_h"));t.parent().find(".CodeMirror").css("height",Math.max(200,n+e.pageY-t.attr("data-start_y")))}},{key:"onReleaseTool",value:function(){$("body").off("mousemove",e.onDragTool),$(window).off("mouseup",e.onReleaseTool)}},{key:"initFieldCollapse",value:function(){$(document).on("click, change",'[data-bb-toggle="collapse"]',(function(e){var t=$(this).data("bb-target"),n=null;switch(e.currentTarget.type){case"checkbox":n=$(document).find(t);var o=$(this).data("bb-reverse"),a=$(this).prop("checked");o?a?n.slideUp():n.slideDown():a?n.slideDown():n.slideUp();break;case"radio":case"select-one":n=$(document).find("".concat(t,'[data-bb-value="').concat($(this).val(),'"]'));var i=$(document).find("".concat(t,"[data-bb-value]"));n.length?(i.not(n).slideUp(),n.slideDown()):i.slideUp();break;case"button":(n=$(document).find(t)).length&&n.slideToggle();break;default:console.warn("[Botble] Unknown type ".concat(e.currentTarget.type," of collapse"))}}))}},{key:"initTreeCheckboxes",value:function(){var e=function e(t){var n=$(t).closest("ul").closest("li").find("> .form-check > input[type=checkbox]"),o=n.parent().parent();o.find("ul input[type=checkbox]:checked").length>0?o.find("ul input[type=checkbox]:checked").length===o.find("ul input[type=checkbox]").length?(n.prop("indeterminate",!1),n.prop("checked",!0)):n.prop("indeterminate",!0):(n.prop("indeterminate",!1),n.prop("checked",!1)),n.length>0&&e(n)},t='[data-bb-toggle="tree-checkboxes"] input[type="checkbox"]';$(document).on("click",t,(function(){var e=$(this);e.parent().parent().find("ul input[type=checkbox]").each((function(){e.prop("checked")?$(this).prop("checked",!0):($(this).prop("checked",!1),$(this).prop("indeterminate",!1))}))})),$(document).on("click",t,(function(){e(this)}))}},{key:"initCodeEditorComponent",value:function(){$(document).find("textarea[data-bb-code-editor]").each((function(){e.initCodeEditor(this,this.dataset.mode||"htmlmixed")}))}},{key:"showButtonLoading",value:function(e){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"start";if(arguments.length>1&&void 0!==arguments[1]&&!arguments[1]||!e){var n='<span class="spinner-border spinner-border-sm me-2" role="status"></span>',o=$(e).find("svg");o.length&&o.addClass("d-none"),"start"===t?$(e).prepend(n):"end"===t&&$(e).append(n)}else $(e).addClass("btn-loading").attr("disabled",!0)}},{key:"hideButtonLoading",value:function(e){e&&($(e).hasClass("btn-loading")?$(e).removeClass("btn-loading").removeAttr("disabled"):($(e).find(".spinner-border").remove(),$(e).find("svg").removeClass("d-none")))}},{key:"showLoading",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;e||(e=document.querySelector(".page-wrapper")),$(e).find(".loading-spinner").length||($(e).addClass("position-relative"),$(e).append('<div class="loading-spinner"></div>'))}},{key:"hideLoading",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;e||(e=document.querySelector(".page-wrapper")),$(e).removeClass("position-relative"),$(e).find(".loading-spinner").remove()}},{key:"select",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(jQuery().select2){t=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?r(Object(n),!0).forEach((function(t){p(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):r(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({width:"100%",placeholder:$(e).attr("placeholder")||null},t);var n=$(e).closest("div[data-select2-dropdown-parent]")||$(e).closest(".modal");n.length&&(t.dropdownParent=n,t.width="100%",t.minimumResultsForSearch=-1),$(e).select2(t)}}},{key:"lightbox",value:function(e){var t=new FsLightbox;return Array.isArray(e)&&(t.props.sources=e,t.open()),t}},{key:"initLightbox",value:function(){var t=window.lightboxInstance||{},n=document.querySelectorAll("a[data-bb-lightbox]");n.length&&(n.forEach((function(n){var o=n.dataset.bbLightbox;t[o]||(t[o]=e.lightbox());var a=n.href;t[o].props.sources.push(a),t[o].elements.a.push(n);var i=t[o].props.sources.length-1;n.addEventListener("click",(function(e){e.preventDefault(),t[o].open(i)}))})),window.lightboxInstance=t)}},{key:"initColorPicker",value:function(){document.querySelector("[data-bb-color-picker]")&&$("[data-bb-color-picker]").each((function(e,t){var n=$(t),o={allowEmpty:!0,color:n.val()||"rgb(51, 51, 51)",showInput:!0,containerClassName:"full-spectrum",showInitial:!0,showSelectionPalette:!1,showPalette:!0,showAlpha:!0,preferredFormat:"hex",showButtons:!1,palette:[["rgb(0, 0, 0)","rgb(102, 102, 102)","rgb(183, 183, 183)","rgb(217, 217, 217)","rgb(239, 239, 239)","rgb(243, 243, 243)","rgb(255, 255, 255)","rgb(230, 184, 175)","rgb(244, 204, 204)","rgb(252, 229, 205)","rgb(255, 242, 204)","rgb(217, 234, 211)","rgb(208, 224, 227)","rgb(201, 218, 248)","rgb(207, 226, 243)","rgb(217, 210, 233)","rgb(234, 209, 220)","rgb(221, 126, 107)","rgb(234, 153, 153)","rgb(249, 203, 156)","rgb(255, 229, 153)","rgb(182, 215, 168)","rgb(162, 196, 201)","rgb(164, 194, 244)","rgb(159, 197, 232)","rgb(180, 167, 214)","rgb(213, 166, 189)","rgb(204, 65, 37)","rgb(224, 102, 102)","rgb(246, 178, 107)","rgb(255, 217, 102)","rgb(147, 196, 125)","rgb(118, 165, 175)","rgb(109, 158, 235)","rgb(111, 168, 220)","rgb(142, 124, 195)","rgb(194, 123, 160)","rgb(166, 28, 0)","rgb(204, 0, 0)","rgb(230, 145, 56)","rgb(241, 194, 50)","rgb(106, 168, 79)","rgb(69, 129, 142)","rgb(60, 120, 216)","rgb(61, 133, 198)","rgb(103, 78, 167)","rgb(166, 77, 121)","rgb(133, 32, 12)","rgb(153, 0, 0)","rgb(180, 95, 6)","rgb(191, 144, 0)","rgb(56, 118, 29)","rgb(19, 79, 92)","rgb(17, 85, 204)","rgb(11, 83, 148)","rgb(53, 28, 117)","rgb(116, 27, 71)","rgb(91, 15, 0)","rgb(102, 0, 0)","rgb(120, 63, 4)","rgb(127, 96, 0)","rgb(39, 78, 19)","rgb(12, 52, 61)","rgb(28, 69, 135)","rgb(7, 55, 99)","rgb(32, 18, 77)","rgb(76, 17, 48)","rgb(152, 0, 0)","rgb(255, 0, 0)","rgb(255, 153, 0)","rgb(255, 255, 0)","rgb(0, 255, 0)","rgb(0, 255, 255)","rgb(74, 134, 232)","rgb(0, 0, 255)","rgb(153, 0, 255)","rgb(255, 0, 255)"]],change:function(e){e&&n.val(e.toRgbString())}},a=n.closest(".modal");a.length&&(o.appendTo=a),n.spectrum(o)}))}},{key:"initClipboard",value:function(){$(document).on("click",'[data-bb-toggle="clipboard"]',function(){var t=d(c().mark((function t(n){var o,a,i,r,l,s;return c().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return n.preventDefault(),o=$(n.currentTarget),a=o.data("clipboard-message"),i=o.data("clipboard-action")||"copy",r="cut"===i.toLowerCase(),(l=o.data("clipboard-text"))||(s=$(o.data("clipboard-target"))).length>0&&(l=s.val(),r&&s.val("")),t.next=9,e.copyToClipboard(l);case 9:a&&e.showSuccess(a);case 10:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())}},{key:"initTreeCategoriesSelect",value:function(){var t=document.querySelector('[data-bb-toggle="tree-categories-select"]');t&&e.select(t,{render:{option:function(e){return"<div>".concat(e.renderOption,"</div>")},item:function(e){return"<div>".concat(e.renderItem,"</div>")}}})}},{key:"initDropdownCheckboxes",value:function(){var e=function e(t){var n=t?$(t.currentTarget).closest('[data-bb-toggle="dropdown-checkboxes"]'):$('[data-bb-toggle="dropdown-checkboxes"]');if(Array.isArray(n))n.forEach((function(t){e(t)}));else{var o=n.find('input[type="checkbox"]:checked'),a=n.find("> span");if(o.length)if(o.length>3)a.text(o.length+" "+n.data("selected-text"));else{var i=[];o.each((function(){i.push($(this).siblings(".form-check-label").text().trim())})),a.text(i.join(", "))}else a.text(n.data("placeholder")||" ")}};e(),$(document).on("click",'[data-bb-toggle="dropdown-checkboxes"] input[type="checkbox"]',(function(t){e(t);var n=$(t.currentTarget).closest('[data-bb-toggle="dropdown-checkboxes"]'),o=n.find(".multi-checklist-selected");if($(t.currentTarget).is(":checked")){var a='<input type="hidden" name="'.concat(n.data("name"),'" value="').concat($(t.currentTarget).val(),'">');o.append(a)}else o.find('input[value="'.concat($(t.currentTarget).val(),'"]')).remove()})),$(document).on("click",'[data-bb-toggle="dropdown-checkboxes"] > span',(function(e){e.stopPropagation();var t=$(this),n=t.siblings('input[type="text"]'),o=t.siblings(".dropdown-menu"),a=t.closest('[data-bb-toggle="dropdown-checkboxes"]');if(o.addClass("show"),t.hide(),n.show().trigger("focus"),a.data("ajax-url")){var i=a.data("name");$httpClient.make().withLoading(o).get(a.data("ajax-url")).then((function(e){var t=e.data,n="";Object.keys(t).map((function(e){n+='<li>\n                    <label class="form-check">\n                        <input type="checkbox" id="__id__" class="form-check-input" value="__value__">\n                        <span class="form-check-label">\n                            __label__\n                        </span>\n                    </label>\n                </li>'.replace(/__id__/g,"".concat(i,"-").concat(e)).replace(/__value__/g,e).replace(/__label__/g,t[e])})),o.find("ul").html(n),a.find(".multi-checklist-selected").find('input[type="hidden"]').each((function(){var e=$(this);o.find('input[value="'.concat(e.val(),'"]')).prop("checked",!0)}))}))}})),$(document).on("click",(function(e){var t=$(e.target),n=$('[data-bb-toggle="dropdown-checkboxes"]');t.closest('[data-bb-toggle="dropdown-checkboxes"]').length||(n.find("> .dropdown-menu").removeClass("show"),n.find("> span").show(),n.find('> input[type="text"]').val("").hide(),n.data("ajax-url")&&n.find("> .dropdown-menu ul").html('<div class="py-5"></div>'))})),$(document).on("keyup",'[data-bb-toggle="dropdown-checkboxes"] input[type="text"]',(function(){var e=$(this),t=e.closest('[data-bb-toggle="dropdown-checkboxes"]').find("li"),n=e.val().trim().toLowerCase();n.length?(t.hide(),t.each((function(){var e=$(this);-1!==e.find(".form-check-label").text().trim().toLowerCase().indexOf(n)&&e.show()}))):t.show()}))}},{key:"initEditable",value:function(){var t=$(".editable");t.length&&t.editable({mode:"inline",success:function(t){t.error&&t.message&&e.showError(t.message)},error:function(t){e.handleError(t)}})}},{key:"unmaskInputNumber",value:function(e,t){if(jQuery().inputmask)return e.find("input.input-mask-number").map((function(e,n){var o=$(n);o.inputmask&&($.isArray(t)?t[o.attr("name")]=o.inputmask("unmaskedvalue"):t.append(o.attr("name"),o.inputmask("unmaskedvalue")))})),t}}],n&&u(t.prototype,n),o&&u(t,o),Object.defineProperty(t,"prototype",{writable:!1}),e}();p(h,"noticesTimeout",{}),p(h,"noticesTimeoutCount",500),$((function(){$.ajaxSetup({headers:{"X-CSRF-TOKEN":$('meta[name="csrf-token"]').attr("content")}}),new h,window.Botble=h}))})();