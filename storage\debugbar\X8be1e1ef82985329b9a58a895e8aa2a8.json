{"__meta": {"id": "X8be1e1ef82985329b9a58a895e8aa2a8", "datetime": "2025-07-30 21:53:36", "utime": **********.238723, "method": "GET", "uri": "/checkout/241afa5f1f6e9ca99f6cbf0bf30e3abd?_token=jh1lEMPqUbufsTpHwVjiRRuAGAYe8v5WBn4lkaI4&checkout-token=241afa5f1f6e9ca99f6cbf0bf30e3abd&coupon_code=&address%5Bname%5D=Alex%20Fleming&address%5Bemail%5D=alex%40goalconversion.com&address%5Bphone%5D=**********&address%5Baddress%5D=26%20Duncan%20St&address%5Bzip_code%5D=M5V%202B9&address%5Bcountry%5D=2&address%5Bstate%5D=8&address%5Bcity%5D=11&password=&password_confirmation=&shipping_option=9&shipping_method=default&amount=51.8&currency=USD&payment_method=bank_transfer&description=&tax_information%5Bcompany_name%5D=&tax_information%5Bcompany_address%5D=&tax_information%5Bcompany_tax_code%5D=&tax_information%5Bcompany_email%5D=payment_method=bank_transfer", "ip": "127.0.0.1"}, "php": {"version": "8.3.17", "interface": "apache2handler"}, "messages": {"count": 4, "messages": [{"message": "[21:53:24] LOG.warning: Creation of dynamic property Botble\\Ecommerce\\Cart\\CartItem::$created_at is deprecated in D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php on line 117", "message_html": null, "is_string": false, "label": "warning", "time": **********.205387, "xdebug_link": null, "collector": "log"}, {"message": "[21:53:24] LOG.warning: Creation of dynamic property Botble\\Ecommerce\\Cart\\CartItem::$updated_at is deprecated in D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php on line 117", "message_html": null, "is_string": false, "label": "warning", "time": **********.205538, "xdebug_link": null, "collector": "log"}, {"message": "[21:53:27] LOG.warning: Creation of dynamic property Botble\\Ecommerce\\Cart\\CartItem::$created_at is deprecated in D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Cart\\CartItem.php on line 92", "message_html": null, "is_string": false, "label": "warning", "time": **********.216803, "xdebug_link": null, "collector": "log"}, {"message": "[21:53:27] LOG.warning: Creation of dynamic property Botble\\Ecommerce\\Cart\\CartItem::$updated_at is deprecated in D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Cart\\CartItem.php on line 93", "message_html": null, "is_string": false, "label": "warning", "time": **********.216958, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753898003.407421, "end": **********.23877, "duration": 12.831348896026611, "duration_str": "12.83s", "measures": [{"label": "Booting", "start": 1753898003.407421, "relative_start": 0, "end": **********.164994, "relative_end": **********.164994, "duration": 0.7575728893280029, "duration_str": "758ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.16501, "relative_start": 0.7575888633728027, "end": **********.238774, "relative_end": 4.0531158447265625e-06, "duration": 12.073764085769653, "duration_str": "12.07s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 61970048, "peak_usage_str": "59MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 46, "templates": [{"name": "plugins/ecommerce::orders.checkout", "param_count": null, "params": [], "start": **********.28972, "type": "blade", "hash": "tokenshippingdefaultShippingMethoddefaultShippingOptionshippingAmountpromotionDiscountAmountcouponDiscountAmountsessionCheckoutDataproductsisShowAddressFormdiscounts", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Forders%2Fcheckout.blade.php&line=1", "ajax": false, "filename": "checkout.blade.php", "line": ""}}, {"name": "plugins/payment::partials.header", "param_count": null, "params": [], "start": **********.291351, "type": "blade", "hash": "__envapperrorstokenshippingdefaultShippingMethoddefaultShippingOptionshippingAmountpromotionDiscountAmountcouponDiscountAmountsessionCheckoutDataproductsisShowAddressFormdiscountsrawTotalorderAmount", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fpayment%2Fresources%2Fviews%2Fpartials%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": ""}}, {"name": "plugins/ecommerce::orders.partials.logo", "param_count": null, "params": [], "start": **********.293863, "type": "blade", "hash": "__envapperrorstokenshippingdefaultShippingMethoddefaultShippingOptionshippingAmountpromotionDiscountAmountcouponDiscountAmountsessionCheckoutDataproductsisShowAddressFormdiscountsrawTotalorderAmountcomponent", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Forders%2Fpartials%2Flogo.blade.php&line=1", "ajax": false, "filename": "logo.blade.php", "line": ""}}, {"name": "plugins/ecommerce::orders.checkout.products", "param_count": null, "params": [], "start": **********.455288, "type": "blade", "hash": "products", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Forders%2Fcheckout%2Fproducts.blade.php&line=1", "ajax": false, "filename": "products.blade.php", "line": ""}}, {"name": "plugins/ecommerce::orders.checkout.product", "param_count": null, "params": [], "start": **********.456191, "type": "blade", "hash": "__envapperrorsproducts__currentLoopDataproductkeyloopcartItem", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Forders%2Fcheckout%2Fproduct.blade.php&line=1", "ajax": false, "filename": "product.blade.php", "line": ""}}, {"name": "plugins/ecommerce::themes.includes.cart-item-options-extras", "param_count": null, "params": [], "start": **********.460569, "type": "blade", "hash": "__envapperrorsproducts__currentLoopDataproductkeyloopcartItemoptions", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Fthemes%2Fincludes%2Fcart-item-options-extras.blade.php&line=1", "ajax": false, "filename": "cart-item-options-extras.blade.php", "line": ""}}, {"name": "plugins/ecommerce::themes.discounts.partials.form", "param_count": null, "params": [], "start": 1753898011.601926, "type": "blade", "hash": "__envapperrorstokenshippingdefaultShippingMethoddefaultShippingOptionshippingAmountpromotionDiscountAmountcouponDiscountAmountsessionCheckoutDataproductsisShowAddressFormdiscountsrawTotalorderAmountcomponent", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Fthemes%2Fdiscounts%2Fpartials%2Fform.blade.php&line=1", "ajax": false, "filename": "form.blade.php", "line": ""}}, {"name": "plugins/ecommerce::themes.discounts.partials.apply-coupon", "param_count": null, "params": [], "start": 1753898011.603067, "type": "blade", "hash": "__envapperrorstokenshippingdefaultShippingMethoddefaultShippingOptionshippingAmountpromotionDiscountAmountcouponDiscountAmountsessionCheckoutDataproductsisShowAddressFormdiscountsrawTotalorderAmountcomponent", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Fthemes%2Fdiscounts%2Fpartials%2Fapply-coupon.blade.php&line=1", "ajax": false, "filename": "apply-coupon.blade.php", "line": ""}}, {"name": "plugins/ecommerce::orders.partials.logo", "param_count": null, "params": [], "start": 1753898011.603557, "type": "blade", "hash": "__envapperrorstokenshippingdefaultShippingMethoddefaultShippingOptionshippingAmountpromotionDiscountAmountcouponDiscountAmountsessionCheckoutDataproductsisShowAddressFormdiscountsrawTotalorderAmountcomponent", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Forders%2Fpartials%2Flogo.blade.php&line=1", "ajax": false, "filename": "logo.blade.php", "line": ""}}, {"name": "plugins/ecommerce::orders.partials.address-form", "param_count": null, "params": [], "start": 1753898011.606936, "type": "blade", "hash": "__envapperrorstokenshippingdefaultShippingMethoddefaultShippingOptionshippingAmountpromotionDiscountAmountcouponDiscountAmountsessionCheckoutDataproductsisShowAddressFormdiscountsrawTotalorderAmountcomponent", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Forders%2Fpartials%2Faddress-form.blade.php&line=1", "ajax": false, "filename": "address-form.blade.php", "line": ""}}, {"name": "core/base::forms.partials.error", "param_count": null, "params": [], "start": 1753898011.612245, "type": "blade", "hash": "nameerrors", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": ""}}, {"name": "core/base::forms.partials.error", "param_count": null, "params": [], "start": 1753898011.612878, "type": "blade", "hash": "nameerrors", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": ""}}, {"name": "core/base::forms.partials.error", "param_count": null, "params": [], "start": 1753898011.613243, "type": "blade", "hash": "nameerrors", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": ""}}, {"name": "core/base::forms.partials.error", "param_count": null, "params": [], "start": 1753898011.613927, "type": "blade", "hash": "nameerrors", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": ""}}, {"name": "core/base::forms.partials.error", "param_count": null, "params": [], "start": 1753898011.614272, "type": "blade", "hash": "nameerrors", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": ""}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::icon", "param_count": null, "params": [], "start": 1753898011.617965, "type": "blade", "hash": "nameattributesslot__laravel_slots", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ficon.blade.php&line=1", "ajax": false, "filename": "icon.blade.php", "line": ""}}, {"name": "core/base::components.icons.chevron-down", "param_count": null, "params": [], "start": 1753898011.618929, "type": "blade", "hash": "__envapperrorsnameattributesslot__laravel_slots__value__keyclasssizewrapper", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ficons%2Fchevron-down.blade.php&line=1", "ajax": false, "filename": "chevron-down.blade.php", "line": ""}}, {"name": "core/base::forms.partials.error", "param_count": null, "params": [], "start": 1753898011.619445, "type": "blade", "hash": "nameerrors", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": ""}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::icon", "param_count": null, "params": [], "start": 1753898011.638473, "type": "blade", "hash": "nameattributesslot__laravel_slots", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ficon.blade.php&line=1", "ajax": false, "filename": "icon.blade.php", "line": ""}}, {"name": "core/base::components.icons.chevron-down", "param_count": null, "params": [], "start": 1753898011.639207, "type": "blade", "hash": "__envapperrorsnameattributesslot__laravel_slots__value__keyclasssizewrapper", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ficons%2Fchevron-down.blade.php&line=1", "ajax": false, "filename": "chevron-down.blade.php", "line": ""}}, {"name": "core/base::forms.partials.error", "param_count": null, "params": [], "start": 1753898011.639625, "type": "blade", "hash": "nameerrors", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": ""}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::icon", "param_count": null, "params": [], "start": 1753898011.665062, "type": "blade", "hash": "nameattributesslot__laravel_slots", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ficon.blade.php&line=1", "ajax": false, "filename": "icon.blade.php", "line": ""}}, {"name": "core/base::components.icons.chevron-down", "param_count": null, "params": [], "start": 1753898011.665801, "type": "blade", "hash": "__envapperrorsnameattributesslot__laravel_slots__value__keyclasssizewrapper", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ficons%2Fchevron-down.blade.php&line=1", "ajax": false, "filename": "chevron-down.blade.php", "line": ""}}, {"name": "core/base::forms.partials.error", "param_count": null, "params": [], "start": 1753898011.666188, "type": "blade", "hash": "nameerrors", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": ""}}, {"name": "core/base::forms.partials.error", "param_count": null, "params": [], "start": 1753898011.666677, "type": "blade", "hash": "nameerrors", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": ""}}, {"name": "core/base::forms.partials.error", "param_count": null, "params": [], "start": 1753898011.666995, "type": "blade", "hash": "nameerrors", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": ""}}, {"name": "plugins/ecommerce::orders.partials.shipping-option", "param_count": null, "params": [], "start": 1753898011.668905, "type": "blade", "hash": "__envapperrorstokenshippingdefaultShippingMethoddefaultShippingOptionshippingAmountpromotionDiscountAmountcouponDiscountAmountsessionCheckoutDataproductsisShowAddressFormdiscountsrawTotalorderAmountcomponent__currentLoopDatashippingItemsshippingKeyloopshippingItemshippingOptionattributes", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Forders%2Fpartials%2Fshipping-option.blade.php&line=1", "ajax": false, "filename": "shipping-option.blade.php", "line": ""}}, {"name": "plugins/stripe::methods", "param_count": null, "params": [], "start": 1753898014.687795, "type": "blade", "hash": "amountcurrencynameselecteddefaultselecting", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fstripe%2Fresources%2Fviews%2Fmethods.blade.php&line=1", "ajax": false, "filename": "methods.blade.php", "line": ""}}, {"name": "plugins/paymob::methods", "param_count": null, "params": [], "start": **********.339729, "type": "blade", "hash": "amountcurrencynameselecteddefaultselectingerrorMessageorderId", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fpaymob%2Fresources%2Fviews%2Fmethods.blade.php&line=1", "ajax": false, "filename": "methods.blade.php", "line": ""}}, {"name": "plugins/paystack::methods", "param_count": null, "params": [], "start": **********.340734, "type": "blade", "hash": "amountcurrencynameselecteddefaultselecting", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fpaystack%2Fresources%2Fviews%2Fmethods.blade.php&line=1", "ajax": false, "filename": "methods.blade.php", "line": ""}}, {"name": "plugins/sslcommerz::methods", "param_count": null, "params": [], "start": **********.342239, "type": "blade", "hash": "amountcurrencynameselecteddefaultselecting", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fsslcommerz%2Fresources%2Fviews%2Fmethods.blade.php&line=1", "ajax": false, "filename": "methods.blade.php", "line": ""}}, {"name": "plugins/payment::partials.cod", "param_count": null, "params": [], "start": **********.343408, "type": "blade", "hash": "", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fpayment%2Fresources%2Fviews%2Fpartials%2Fcod.blade.php&line=1", "ajax": false, "filename": "cod.blade.php", "line": ""}}, {"name": "plugins/payment::partials.bank-transfer", "param_count": null, "params": [], "start": **********.345386, "type": "blade", "hash": "", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fpayment%2Fresources%2Fviews%2Fpartials%2Fbank-transfer.blade.php&line=1", "ajax": false, "filename": "bank-transfer.blade.php", "line": ""}}, {"name": "plugins/ecommerce::orders.partials.cod", "param_count": null, "params": [], "start": **********.348342, "type": "blade", "hash": "", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Forders%2Fpartials%2Fcod.blade.php&line=1", "ajax": false, "filename": "cod.blade.php", "line": ""}}, {"name": "core/base::forms.partials.error", "param_count": null, "params": [], "start": **********.022212, "type": "blade", "hash": "nameerrors", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": ""}}, {"name": "plugins/ecommerce::orders.partials.tax-information", "param_count": null, "params": [], "start": **********.023344, "type": "blade", "hash": "__envapperrorstokenshippingdefaultShippingMethoddefaultShippingOptionshippingAmountpromotionDiscountAmountcouponDiscountAmountsessionCheckoutDataproductsisShowAddressFormdiscountsrawTotalorderAmountcomponent__currentLoopDatashippingItemsshippingKeyloopshippingItemshippingOptionpaymentMethods", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Forders%2Fpartials%2Ftax-information.blade.php&line=1", "ajax": false, "filename": "tax-information.blade.php", "line": ""}}, {"name": "core/base::forms.partials.error", "param_count": null, "params": [], "start": **********.024245, "type": "blade", "hash": "nameerrors", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": ""}}, {"name": "core/base::forms.partials.error", "param_count": null, "params": [], "start": **********.024726, "type": "blade", "hash": "nameerrors", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": ""}}, {"name": "core/base::forms.partials.error", "param_count": null, "params": [], "start": **********.025165, "type": "blade", "hash": "nameerrors", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": ""}}, {"name": "core/base::forms.partials.error", "param_count": null, "params": [], "start": **********.025506, "type": "blade", "hash": "nameerrors", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": ""}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::icon", "param_count": null, "params": [], "start": **********.02648, "type": "blade", "hash": "nameattributesslot__laravel_slots", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ficon.blade.php&line=1", "ajax": false, "filename": "icon.blade.php", "line": ""}}, {"name": "core/base::components.icons.arrow-narrow-left", "param_count": null, "params": [], "start": **********.027441, "type": "blade", "hash": "__envapperrorsnameattributesslot__laravel_slots__value__keyclasssizewrapper", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ficons%2Farrow-narrow-left.blade.php&line=1", "ajax": false, "filename": "arrow-narrow-left.blade.php", "line": ""}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::form.index", "param_count": null, "params": [], "start": **********.028959, "type": "blade", "hash": "urlidclassattributesslot__laravel_slots", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": ""}}, {"name": "plugins/payment::partials.footer", "param_count": null, "params": [], "start": **********.030715, "type": "blade", "hash": "__envapperrorstokenshippingdefaultShippingMethoddefaultShippingOptionshippingAmountpromotionDiscountAmountcouponDiscountAmountsessionCheckoutDataproductsisShowAddressFormdiscountsrawTotalorderAmountcomponent__currentLoopDatashippingItemsshippingKeyloopshippingItemshippingOptionpaymentMethods", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fpayment%2Fresources%2Fviews%2Fpartials%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": ""}}, {"name": "core/js-validation::bootstrap", "param_count": null, "params": [], "start": **********.167209, "type": "blade", "hash": "validator", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fjs-validation%2Fresources%2Fviews%2Fbootstrap.blade.php&line=1", "ajax": false, "filename": "bootstrap.blade.php", "line": ""}}, {"name": "plugins/ecommerce::orders.master", "param_count": null, "params": [], "start": **********.170963, "type": "blade", "hash": "__envapperrorstokenshippingdefaultShippingMethoddefaultShippingOptionshippingAmountpromotionDiscountAmountcouponDiscountAmountsessionCheckoutDataproductsisShowAddressFormdiscountsrawTotalorderAmountcomponent__currentLoopDatashippingItemsshippingKeyloopshippingItemshippingOptionpaymentMethods", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Forders%2Fmaster.blade.php&line=1", "ajax": false, "filename": "master.blade.php", "line": ""}}]}, "route": {"uri": "GET checkout/{token}", "middleware": "web, core, localeSessionRedirect, localizationRedirect", "as": "public.checkout.information", "controller": "Botble\\Ecommerce\\Http\\Controllers\\Fronts\\PublicCheckoutController@getCheckout", "namespace": "Botble\\Ecommerce\\Http\\Controllers\\Fronts", "prefix": "/checkout/{token}", "where": [], "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FHttp%2FControllers%2FFronts%2FPublicCheckoutController.php&line=51\" onclick=\"\">platform/plugins/ecommerce/src/Http/Controllers/Fronts/PublicCheckoutController.php:51-302</a>"}, "queries": {"nb_statements": 54, "nb_failed_statements": 0, "accumulated_duration": 0.12270999999999999, "accumulated_duration_str": "123ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Footprints/TrackingFilter.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Services\\Footprints\\TrackingFilter.php", "line": 45}], "start": **********.218587, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:55", "source": "platform/core/base/src/Models/BaseQueryBuilder.php:55", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=55", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "55"}, "connection": "chubbybyp-v2", "start_percent": 0, "width_percent": 0.456}, {"sql": "select * from `ec_currencies` order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/CurrencySupport.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\CurrencySupport.php", "line": 109}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/CurrencySupport.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\CurrencySupport.php", "line": 43}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/helpers/currencies.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\helpers\\currencies.php", "line": 141}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 88}], "start": **********.234111, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:55", "source": "platform/core/base/src/Models/BaseQueryBuilder.php:55", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=55", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "55"}, "connection": "chubbybyp-v2", "start_percent": 0.456, "width_percent": 0.407}, {"sql": "select distinct `ec_products`.*, `products_with_final_price`.`final_price` from `ec_products` inner join\n(\nSELECT DISTINCT\nec_products.id,\nCASE\nWHEN (\nec_products.sale_type = 0 AND\nec_products.sale_price <> 0\n) THEN ec_products.sale_price\nWHEN (\nec_products.sale_type = 0 AND\nec_products.sale_price = 0\n) THEN ec_products.price\nWHEN (\nec_products.sale_type = 1 AND\n(\nec_products.start_date > '2025-07-30 21:53:25' OR\nec_products.end_date < '2025-07-30 21:53:25'\n)\n) THEN ec_products.price\nWHEN (\nec_products.sale_type = 1 AND\nec_products.start_date <= '2025-07-30 21:53:25' AND\nec_products.end_date >= '2025-07-30 21:53:25'\n) THEN ec_products.sale_price\nWHEN (\nec_products.sale_type = 1 AND\nec_products.start_date IS NULL AND\nec_products.end_date >= '2025-07-30 21:53:25'\n) THEN ec_products.sale_price\nWHEN (\nec_products.sale_type = 1 AND\nec_products.start_date <= '2025-07-30 21:53:25' AND\nec_products.end_date IS NULL\n) THEN ec_products.sale_price\nELSE ec_products.price\nEND AS final_price\nFROM ec_products\n) AS products_with_final_price\non `products_with_final_price`.`id` = `ec_products`.`id` where `status` = 'published' and `ec_products`.`id` in (844) order by `order` asc, `created_at` desc", "type": "query", "params": [], "bindings": ["published", "844"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 16, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 370}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 671}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 88}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 717}], "start": **********.4821198, "duration": 0.00219, "duration_str": "2.19ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:55", "source": "platform/core/base/src/Models/BaseQueryBuilder.php:55", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=55", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "55"}, "connection": "chubbybyp-v2", "start_percent": 0.864, "width_percent": 1.785}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (844) and `slugs`.`reference_type` = 'Botble\\Ecommerce\\Models\\Product'", "type": "query", "params": [], "bindings": ["Botble\\Ecommerce\\Models\\Product"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 22, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 370}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 671}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 88}], "start": **********.489183, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:55", "source": "platform/core/base/src/Models/BaseQueryBuilder.php:55", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=55", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "55"}, "connection": "chubbybyp-v2", "start_percent": 2.649, "width_percent": 0.416}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`is_default` = 1 and `ec_product_variations`.`configurable_product_id` in (844)", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 22, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 370}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 671}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 88}], "start": **********.493152, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:55", "source": "platform/core/base/src/Models/BaseQueryBuilder.php:55", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=55", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "55"}, "connection": "chubbybyp-v2", "start_percent": 3.064, "width_percent": 0.448}, {"sql": "select `ec_product_collections`.*, `ec_product_collection_products`.`product_id` as `pivot_product_id`, `ec_product_collection_products`.`product_collection_id` as `pivot_product_collection_id` from `ec_product_collections` inner join `ec_product_collection_products` on `ec_product_collections`.`id` = `ec_product_collection_products`.`product_collection_id` where `ec_product_collection_products`.`product_id` in (844)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 20, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 370}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 671}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 88}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 717}], "start": **********.500799, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:55", "source": "platform/core/base/src/Models/BaseQueryBuilder.php:55", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=55", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "55"}, "connection": "chubbybyp-v2", "start_percent": 3.512, "width_percent": 0.448}, {"sql": "select `ec_product_labels`.*, `ec_product_label_products`.`product_id` as `pivot_product_id`, `ec_product_label_products`.`product_label_id` as `pivot_product_label_id` from `ec_product_labels` inner join `ec_product_label_products` on `ec_product_labels`.`id` = `ec_product_label_products`.`product_label_id` where `ec_product_label_products`.`product_id` in (844)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 20, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 370}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 671}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 88}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 717}], "start": **********.5030499, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:55", "source": "platform/core/base/src/Models/BaseQueryBuilder.php:55", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=55", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "55"}, "connection": "chubbybyp-v2", "start_percent": 3.961, "width_percent": 0.342}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`product_id` in (844)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 22, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 370}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 671}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 88}], "start": **********.504886, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:55", "source": "platform/core/base/src/Models/BaseQueryBuilder.php:55", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=55", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "55"}, "connection": "chubbybyp-v2", "start_percent": 4.303, "width_percent": 0.293}, {"sql": "select * from `ec_products` where `ec_products`.`id` in (808)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 27, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 28, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 370}, {"index": 29, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 671}], "start": **********.506847, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:55", "source": "platform/core/base/src/Models/BaseQueryBuilder.php:55", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=55", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "55"}, "connection": "chubbybyp-v2", "start_percent": 4.596, "width_percent": 0.318}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (808) and `slugs`.`reference_type` = 'Botble\\Ecommerce\\Models\\Product'", "type": "query", "params": [], "bindings": ["Botble\\Ecommerce\\Models\\Product"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 27, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 33, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 34, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 370}], "start": **********.508446, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:55", "source": "platform/core/base/src/Models/BaseQueryBuilder.php:55", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=55", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "55"}, "connection": "chubbybyp-v2", "start_percent": 4.914, "width_percent": 0.302}, {"sql": "select distinct `ec_product_variations`.`product_id`, `ec_product_variations`.`configurable_product_id`, `ec_product_attributes`.*, `ec_product_attribute_sets`.`title` as `attribute_set_title`, `ec_product_attribute_sets`.`slug` as `attribute_set_slug` from `ec_product_variations` inner join `ec_product_variation_items` on `ec_product_variation_items`.`variation_id` = `ec_product_variations`.`id` inner join `ec_product_attributes` on `ec_product_attributes`.`id` = `ec_product_variation_items`.`attribute_id` inner join `ec_product_attribute_sets` on `ec_product_attribute_sets`.`id` = `ec_product_attributes`.`attribute_set_id` where `ec_product_variations`.`product_id` in (844) order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 22, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 370}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 671}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 88}], "start": **********.514702, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:55", "source": "platform/core/base/src/Models/BaseQueryBuilder.php:55", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=55", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "55"}, "connection": "chubbybyp-v2", "start_percent": 5.216, "width_percent": 0.513}, {"sql": "select exists(select * from `countries`) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 480}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 179}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 546}, {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleTaxService.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Services\\HandleTaxService.php", "line": 20}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Fronts/PublicCheckoutController.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Fronts\\PublicCheckoutController.php", "line": 119}], "start": **********.521912, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "EcommerceHelper.php:480", "source": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php:480", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FEcommerceHelper.php&line=480", "ajax": false, "filename": "EcommerceHelper.php", "line": "480"}, "connection": "chubbybyp-v2", "start_percent": 5.729, "width_percent": 0.407}, {"sql": "select exists(select * from `states`) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 481}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 179}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 546}, {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleTaxService.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Services\\HandleTaxService.php", "line": 20}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Fronts/PublicCheckoutController.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Fronts\\PublicCheckoutController.php", "line": 119}], "start": **********.524374, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "EcommerceHelper.php:481", "source": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php:481", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FEcommerceHelper.php&line=481", "ajax": false, "filename": "EcommerceHelper.php", "line": "481"}, "connection": "chubbybyp-v2", "start_percent": 6.136, "width_percent": 0.31}, {"sql": "select `name`, `id` from `countries` where `status` = 'published' order by `order` asc, `name` asc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 185}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 546}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleTaxService.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Services\\HandleTaxService.php", "line": 20}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Fronts/PublicCheckoutController.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Fronts\\PublicCheckoutController.php", "line": 119}], "start": **********.525887, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:55", "source": "platform/core/base/src/Models/BaseQueryBuilder.php:55", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=55", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "55"}, "connection": "chubbybyp-v2", "start_percent": 6.446, "width_percent": 0.53}, {"sql": "select `ec_taxes`.*, `ec_tax_products`.`product_id` as `pivot_product_id`, `ec_tax_products`.`tax_id` as `pivot_tax_id` from `ec_taxes` inner join `ec_tax_products` on `ec_taxes`.`id` = `ec_tax_products`.`tax_id` where `ec_tax_products`.`product_id` = 808", "type": "query", "params": [], "bindings": ["808"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleTaxService.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Services\\HandleTaxService.php", "line": 54}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleTaxService.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Services\\HandleTaxService.php", "line": 40}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Fronts/PublicCheckoutController.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Fronts\\PublicCheckoutController.php", "line": 119}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.804622, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:28", "source": "platform/core/base/src/Models/BaseModel.php:28", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseModel.php&line=28", "ajax": false, "filename": "BaseModel.php", "line": "28"}, "connection": "chubbybyp-v2", "start_percent": 6.976, "width_percent": 0.448}, {"sql": "select distinct `ec_products`.* from `ec_products` inner join\n(\nSELECT DISTINCT\nec_products.id,\nCASE\nWHEN (\nec_products.sale_type = 0 AND\nec_products.sale_price <> 0\n) THEN ec_products.sale_price\nWHEN (\nec_products.sale_type = 0 AND\nec_products.sale_price = 0\n) THEN ec_products.price\nWHEN (\nec_products.sale_type = 1 AND\n(\nec_products.start_date > '2025-07-30 21:53:27' OR\nec_products.end_date < '2025-07-30 21:53:27'\n)\n) THEN ec_products.price\nWHEN (\nec_products.sale_type = 1 AND\nec_products.start_date <= '2025-07-30 21:53:27' AND\nec_products.end_date >= '2025-07-30 21:53:27'\n) THEN ec_products.sale_price\nWHEN (\nec_products.sale_type = 1 AND\nec_products.start_date IS NULL AND\nec_products.end_date >= '2025-07-30 21:53:27'\n) THEN ec_products.sale_price\nWHEN (\nec_products.sale_type = 1 AND\nec_products.start_date <= '2025-07-30 21:53:27' AND\nec_products.end_date IS NULL\n) THEN ec_products.sale_price\nELSE ec_products.price\nEND AS final_price\nFROM ec_products\n) AS products_with_final_price\non `products_with_final_price`.`id` = `ec_products`.`id` where `status` = 'published' and `ec_products`.`id` in (844) order by `ec_products`.`order` asc, `ec_products`.`created_at` desc", "type": "query", "params": [], "bindings": ["published", "844"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 16, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 370}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 671}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 88}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/helpers/products.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\helpers\\products.php", "line": 47}], "start": **********.184539, "duration": 0.00191, "duration_str": "1.91ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:55", "source": "platform/core/base/src/Models/BaseQueryBuilder.php:55", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=55", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "55"}, "connection": "chubbybyp-v2", "start_percent": 7.424, "width_percent": 1.557}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (844) and `slugs`.`reference_type` = 'Botble\\Ecommerce\\Models\\Product'", "type": "query", "params": [], "bindings": ["Botble\\Ecommerce\\Models\\Product"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 22, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 370}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 671}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 88}], "start": **********.1876268, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:55", "source": "platform/core/base/src/Models/BaseQueryBuilder.php:55", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=55", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "55"}, "connection": "chubbybyp-v2", "start_percent": 8.981, "width_percent": 0.196}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`is_default` = 1 and `ec_product_variations`.`configurable_product_id` in (844)", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 22, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 370}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 671}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 88}], "start": **********.189585, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:55", "source": "platform/core/base/src/Models/BaseQueryBuilder.php:55", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=55", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "55"}, "connection": "chubbybyp-v2", "start_percent": 9.176, "width_percent": 0.187}, {"sql": "select `ec_product_collections`.*, `ec_product_collection_products`.`product_id` as `pivot_product_id`, `ec_product_collection_products`.`product_collection_id` as `pivot_product_collection_id` from `ec_product_collections` inner join `ec_product_collection_products` on `ec_product_collections`.`id` = `ec_product_collection_products`.`product_collection_id` where `ec_product_collection_products`.`product_id` in (844)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 20, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 370}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 671}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 88}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/helpers/products.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\helpers\\products.php", "line": 47}], "start": **********.191273, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:55", "source": "platform/core/base/src/Models/BaseQueryBuilder.php:55", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=55", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "55"}, "connection": "chubbybyp-v2", "start_percent": 9.364, "width_percent": 0.196}, {"sql": "select `ec_product_labels`.*, `ec_product_label_products`.`product_id` as `pivot_product_id`, `ec_product_label_products`.`product_label_id` as `pivot_product_label_id` from `ec_product_labels` inner join `ec_product_label_products` on `ec_product_labels`.`id` = `ec_product_label_products`.`product_label_id` where `ec_product_label_products`.`product_id` in (844)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 20, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 370}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 671}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 88}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/helpers/products.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\helpers\\products.php", "line": 47}], "start": **********.192585, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:55", "source": "platform/core/base/src/Models/BaseQueryBuilder.php:55", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=55", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "55"}, "connection": "chubbybyp-v2", "start_percent": 9.559, "width_percent": 0.236}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`product_id` = 844 and `ec_product_variations`.`product_id` is not null limit 1", "type": "query", "params": [], "bindings": ["844"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 390}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 826}], "start": **********.194661, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:55", "source": "platform/core/base/src/Models/BaseQueryBuilder.php:55", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=55", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "55"}, "connection": "chubbybyp-v2", "start_percent": 9.795, "width_percent": 0.187}, {"sql": "select * from `ec_products` where `ec_products`.`id` = 808 limit 1", "type": "query", "params": [], "bindings": ["808"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 390}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 826}], "start": **********.197375, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:55", "source": "platform/core/base/src/Models/BaseQueryBuilder.php:55", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=55", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "55"}, "connection": "chubbybyp-v2", "start_percent": 9.983, "width_percent": 0.44}, {"sql": "select * from `ec_flash_sales` where date(`end_date`) >= '2025-07-30' and `status` = 'published' order by `created_at` desc", "type": "query", "params": [], "bindings": ["2025-07-30", "published"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/FlashSaleRepository.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\FlashSaleRepository.php", "line": 22}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/FlashSaleSupport.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\FlashSaleSupport.php", "line": 52}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/FlashSaleSupport.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\FlashSaleSupport.php", "line": 22}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 401}], "start": **********.205216, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:55", "source": "platform/core/base/src/Models/BaseQueryBuilder.php:55", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=55", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "55"}, "connection": "chubbybyp-v2", "start_percent": 10.423, "width_percent": 0.399}, {"sql": "select * from `ec_discounts` where `type` = 'promotion' and `start_date` <= '2025-07-30 21:53:27' and (`end_date` is null or `end_date` >= '2025-07-30 21:53:27') and (`target` in ('all-orders', 'amount-minimum-order') or (`target` in ('customer', 'group-products', 'products-by-category', 'specific-product', 'product-variant') and `product_quantity` = 1))", "type": "query", "params": [], "bindings": ["promotion", "2025-07-30 21:53:27", "2025-07-30 21:53:27", "all-orders", "amount-minimum-order", "customer", "group-products", "products-by-category", "specific-product", "product-variant", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/DiscountRepository.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\DiscountRepository.php", "line": 50}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/DiscountSupport.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\DiscountSupport.php", "line": 109}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/DiscountSupport.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\DiscountSupport.php", "line": 42}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 413}], "start": **********.21247, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:55", "source": "platform/core/base/src/Models/BaseQueryBuilder.php:55", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=55", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "55"}, "connection": "chubbybyp-v2", "start_percent": 10.822, "width_percent": 1.076}, {"sql": "select * from `ec_order_addresses` where (`order_id` = 179 and `type` = 'shipping_address') limit 1", "type": "query", "params": [], "bindings": ["179", "shipping_address"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 737}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 661}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Fronts/PublicCheckoutController.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Fronts\\PublicCheckoutController.php", "line": 485}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Fronts/PublicCheckoutController.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Fronts\\PublicCheckoutController.php", "line": 121}], "start": **********.220727, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:55", "source": "platform/core/base/src/Models/BaseQueryBuilder.php:55", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=55", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "55"}, "connection": "chubbybyp-v2", "start_percent": 11.898, "width_percent": 0.546}, {"sql": "update `ec_order_addresses` set `type` = '{\\\"value\\\":\\\"shipping_address\\\",\\\"label\\\":\\\"Shipping address\\\"}' where `id` = 92", "type": "query", "params": [], "bindings": ["{&quot;value&quot;:&quot;shipping_address&quot;,&quot;label&quot;:&quot;Shipping address&quot;}", "92"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 741}, {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 661}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Fronts/PublicCheckoutController.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Fronts\\PublicCheckoutController.php", "line": 485}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Fronts/PublicCheckoutController.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Fronts\\PublicCheckoutController.php", "line": 121}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.230047, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "OrderHelper.php:741", "source": "platform/plugins/ecommerce/src/Supports/OrderHelper.php:741", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FOrderHelper.php&line=741", "ajax": false, "filename": "OrderHelper.php", "line": "741"}, "connection": "chubbybyp-v2", "start_percent": 12.444, "width_percent": 0.872}, {"sql": "select * from `ec_discounts` where `type` = 'promotion' and `start_date` <= '2025-07-30 21:53:27' and (`end_date` is null or `end_date` >= '2025-07-30 21:53:27') and (`target` in ('all-orders', 'amount-minimum-order') or (`target` in ('customer', 'group-products', 'products-by-category', 'specific-product', 'product-variant') and `product_quantity` > 1))", "type": "query", "params": [], "bindings": ["promotion", "2025-07-30 21:53:27", "2025-07-30 21:53:27", "all-orders", "amount-minimum-order", "customer", "group-products", "products-by-category", "specific-product", "product-variant", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/DiscountRepository.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\DiscountRepository.php", "line": 50}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/DiscountSupport.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\DiscountSupport.php", "line": 109}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyPromotionsService.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyPromotionsService.php", "line": 41}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyPromotionsService.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyPromotionsService.php", "line": 17}], "start": **********.241012, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:55", "source": "platform/core/base/src/Models/BaseQueryBuilder.php:55", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=55", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "55"}, "connection": "chubbybyp-v2", "start_percent": 13.316, "width_percent": 0.685}, {"sql": "select * from `ec_shipping` where `country` = '2' limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleShippingFeeService.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Services\\HandleShippingFeeService.php", "line": 120}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleShippingFeeService.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Services\\HandleShippingFeeService.php", "line": 46}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Fronts/PublicCheckoutController.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Fronts\\PublicCheckoutController.php", "line": 184}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.255545, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:55", "source": "platform/core/base/src/Models/BaseQueryBuilder.php:55", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=55", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "55"}, "connection": "chubbybyp-v2", "start_percent": 14, "width_percent": 0.293}, {"sql": "select * from `ec_shipping_rules` where `ec_shipping_rules`.`id` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleShippingFeeService.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Services\\HandleShippingFeeService.php", "line": 175}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleShippingFeeService.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Services\\HandleShippingFeeService.php", "line": 125}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleShippingFeeService.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Services\\HandleShippingFeeService.php", "line": 46}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Fronts/PublicCheckoutController.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Fronts\\PublicCheckoutController.php", "line": 184}], "start": **********.258573, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:55", "source": "platform/core/base/src/Models/BaseQueryBuilder.php:55", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=55", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "55"}, "connection": "chubbybyp-v2", "start_percent": 14.294, "width_percent": 0.269}, {"sql": "select exists(select * from `countries`) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 480}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleShippingFeeService.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Services\\HandleShippingFeeService.php", "line": 225}, {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleShippingFeeService.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Services\\HandleShippingFeeService.php", "line": 125}, {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleShippingFeeService.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Services\\HandleShippingFeeService.php", "line": 46}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Fronts/PublicCheckoutController.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Fronts\\PublicCheckoutController.php", "line": 184}], "start": **********.26238, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "EcommerceHelper.php:480", "source": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php:480", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FEcommerceHelper.php&line=480", "ajax": false, "filename": "EcommerceHelper.php", "line": "480"}, "connection": "chubbybyp-v2", "start_percent": 14.563, "width_percent": 0.342}, {"sql": "select exists(select * from `states`) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 481}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleShippingFeeService.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Services\\HandleShippingFeeService.php", "line": 225}, {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleShippingFeeService.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Services\\HandleShippingFeeService.php", "line": 125}, {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleShippingFeeService.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Services\\HandleShippingFeeService.php", "line": 46}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Fronts/PublicCheckoutController.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Fronts\\PublicCheckoutController.php", "line": 184}], "start": **********.2649798, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "EcommerceHelper.php:481", "source": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php:481", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FEcommerceHelper.php&line=481", "ajax": false, "filename": "EcommerceHelper.php", "line": "481"}, "connection": "chubbybyp-v2", "start_percent": 14.905, "width_percent": 0.342}, {"sql": "select * from `ec_shipping_rules` where (`shipping_id` = 8 and `type` = 'based_on_price' and `from` <= 45 and (`to` is null or `to` >= 45)) or (`shipping_id` = 8 and `type` = 'based_on_weight' and `from` <= 0.01 and (`to` is null or `to` >= 0.01)) or (`shipping_id` = 8 and `type` = 'based_on_location') or (`shipping_id` = 8 and `type` = 'based_on_zipcode' and exists (select * from `ec_shipping_rule_items` where `ec_shipping_rules`.`id` = `ec_shipping_rule_items`.`shipping_rule_id` and (`zip_code` = 'M5V 2B9')))", "type": "query", "params": [], "bindings": ["8", "based_on_price", "45", "45", "8", "based_on_weight", "0.01", "0.01", "8", "based_on_location", "8", "based_on_zipcode", "M5V 2B9"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleShippingFeeService.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Services\\HandleShippingFeeService.php", "line": 254}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleShippingFeeService.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Services\\HandleShippingFeeService.php", "line": 125}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleShippingFeeService.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Services\\HandleShippingFeeService.php", "line": 46}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Fronts/PublicCheckoutController.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Fronts\\PublicCheckoutController.php", "line": 184}], "start": **********.267385, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:55", "source": "platform/core/base/src/Models/BaseQueryBuilder.php:55", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=55", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "55"}, "connection": "chubbybyp-v2", "start_percent": 15.247, "width_percent": 0.326}, {"sql": "select * from `ec_shipping_rule_items` where `ec_shipping_rule_items`.`shipping_rule_id` in (9) and `is_enabled` = 1 order by `adjustment_price` asc", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleShippingFeeService.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Services\\HandleShippingFeeService.php", "line": 254}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleShippingFeeService.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Services\\HandleShippingFeeService.php", "line": 125}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleShippingFeeService.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Services\\HandleShippingFeeService.php", "line": 46}], "start": **********.2689052, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:55", "source": "platform/core/base/src/Models/BaseQueryBuilder.php:55", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=55", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "55"}, "connection": "chubbybyp-v2", "start_percent": 15.573, "width_percent": 0.236}, {"sql": "select * from `ec_discounts` where `type` = 'coupon' and `display_at_checkout` = 1 and `start_date` <= '2025-07-30 21:53:27' and (`end_date` is null or `end_date` >= '2025-07-30 21:53:27') and (`quantity` is null or `quantity` > `total_used`)", "type": "query", "params": [], "bindings": ["coupon", "1", "2025-07-30 21:53:27", "2025-07-30 21:53:27"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Fronts/PublicCheckoutController.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Fronts\\PublicCheckoutController.php", "line": 291}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.284261, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:55", "source": "platform/core/base/src/Models/BaseQueryBuilder.php:55", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=55", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "55"}, "connection": "chubbybyp-v2", "start_percent": 15.81, "width_percent": 0.725}, {"sql": "select exists(select * from `countries`) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 480}, {"index": 13, "namespace": "view", "name": "plugins/ecommerce::orders.partials.address-form", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform/plugins/ecommerce/resources/views/orders/partials/address-form.blade.php", "line": 311}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 125}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1753898011.620199, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "EcommerceHelper.php:480", "source": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php:480", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FEcommerceHelper.php&line=480", "ajax": false, "filename": "EcommerceHelper.php", "line": "480"}, "connection": "chubbybyp-v2", "start_percent": 16.535, "width_percent": 0.334}, {"sql": "select exists(select * from `states`) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 481}, {"index": 13, "namespace": "view", "name": "plugins/ecommerce::orders.partials.address-form", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform/plugins/ecommerce/resources/views/orders/partials/address-form.blade.php", "line": 311}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 125}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1753898011.621808, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "EcommerceHelper.php:481", "source": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php:481", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FEcommerceHelper.php&line=481", "ajax": false, "filename": "EcommerceHelper.php", "line": "481"}, "connection": "chubbybyp-v2", "start_percent": 16.869, "width_percent": 0.285}, {"sql": "select exists(select * from `countries`) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 480}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 220}, {"index": 14, "namespace": "view", "name": "plugins/ecommerce::orders.partials.address-form", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform/plugins/ecommerce/resources/views/orders/partials/address-form.blade.php", "line": 324}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 125}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1753898011.623606, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "EcommerceHelper.php:480", "source": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php:480", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FEcommerceHelper.php&line=480", "ajax": false, "filename": "EcommerceHelper.php", "line": "480"}, "connection": "chubbybyp-v2", "start_percent": 17.154, "width_percent": 0.31}, {"sql": "select exists(select * from `states`) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 481}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 220}, {"index": 14, "namespace": "view", "name": "plugins/ecommerce::orders.partials.address-form", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform/plugins/ecommerce/resources/views/orders/partials/address-form.blade.php", "line": 324}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 125}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1753898011.625529, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "EcommerceHelper.php:481", "source": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php:481", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FEcommerceHelper.php&line=481", "ajax": false, "filename": "EcommerceHelper.php", "line": "481"}, "connection": "chubbybyp-v2", "start_percent": 17.464, "width_percent": 0.848}, {"sql": "select `name`, `id` from `states` where `status` = 'published' and `country_id` = '2' order by `order` asc, `name` asc", "type": "query", "params": [], "bindings": ["published", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 230}, {"index": 18, "namespace": "view", "name": "plugins/ecommerce::orders.partials.address-form", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform/plugins/ecommerce/resources/views/orders/partials/address-form.blade.php", "line": 324}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 125}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1753898011.6283412, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:55", "source": "platform/core/base/src/Models/BaseQueryBuilder.php:55", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=55", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "55"}, "connection": "chubbybyp-v2", "start_percent": 18.311, "width_percent": 0.359}, {"sql": "select exists(select * from `countries`) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 480}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 1273}, {"index": 14, "namespace": "view", "name": "plugins/ecommerce::orders.partials.address-form", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform/plugins/ecommerce/resources/views/orders/partials/address-form.blade.php", "line": 383}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 125}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1753898011.640465, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "EcommerceHelper.php:480", "source": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php:480", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FEcommerceHelper.php&line=480", "ajax": false, "filename": "EcommerceHelper.php", "line": "480"}, "connection": "chubbybyp-v2", "start_percent": 18.67, "width_percent": 0.359}, {"sql": "select exists(select * from `states`) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 481}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 1273}, {"index": 14, "namespace": "view", "name": "plugins/ecommerce::orders.partials.address-form", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform/plugins/ecommerce/resources/views/orders/partials/address-form.blade.php", "line": 383}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 125}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1753898011.642644, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "EcommerceHelper.php:481", "source": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php:481", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FEcommerceHelper.php&line=481", "ajax": false, "filename": "EcommerceHelper.php", "line": "481"}, "connection": "chubbybyp-v2", "start_percent": 19.029, "width_percent": 0.334}, {"sql": "select exists(select * from `countries`) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 480}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 237}, {"index": 14, "namespace": "view", "name": "plugins/ecommerce::orders.partials.address-form", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform/plugins/ecommerce/resources/views/orders/partials/address-form.blade.php", "line": 408}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 125}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1753898011.6444578, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "EcommerceHelper.php:480", "source": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php:480", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FEcommerceHelper.php&line=480", "ajax": false, "filename": "EcommerceHelper.php", "line": "480"}, "connection": "chubbybyp-v2", "start_percent": 19.363, "width_percent": 0.342}, {"sql": "select exists(select * from `states`) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 481}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 237}, {"index": 14, "namespace": "view", "name": "plugins/ecommerce::orders.partials.address-form", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform/plugins/ecommerce/resources/views/orders/partials/address-form.blade.php", "line": 408}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 125}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1753898011.646091, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "EcommerceHelper.php:481", "source": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php:481", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FEcommerceHelper.php&line=481", "ajax": false, "filename": "EcommerceHelper.php", "line": "481"}, "connection": "chubbybyp-v2", "start_percent": 19.705, "width_percent": 0.318}, {"sql": "select `name`, `id` from `cities` where `status` = 'published' and `state_id` = '8' order by `order` asc, `name` asc", "type": "query", "params": [], "bindings": ["published", "8"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 248}, {"index": 18, "namespace": "view", "name": "plugins/ecommerce::orders.partials.address-form", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform/plugins/ecommerce/resources/views/orders/partials/address-form.blade.php", "line": 408}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 125}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1753898011.648308, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:55", "source": "platform/core/base/src/Models/BaseQueryBuilder.php:55", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=55", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "55"}, "connection": "chubbybyp-v2", "start_percent": 20.023, "width_percent": 0.391}, {"sql": "select exists(select * from `countries`) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 480}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 569}, {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Requests/CheckoutRequest.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Http\\Requests\\CheckoutRequest.php", "line": 40}, {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Requests/SaveCheckoutInformationRequest.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Http\\Requests\\SaveCheckoutInformationRequest.php", "line": 11}, {"index": 16, "namespace": null, "name": "platform/core/js-validation/src/JsValidatorFactory.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\js-validation\\src\\JsValidatorFactory.php", "line": 91}], "start": **********.0509639, "duration": 0.00214, "duration_str": "2.14ms", "memory": 0, "memory_str": null, "filename": "EcommerceHelper.php:480", "source": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php:480", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FEcommerceHelper.php&line=480", "ajax": false, "filename": "EcommerceHelper.php", "line": "480"}, "connection": "chubbybyp-v2", "start_percent": 20.414, "width_percent": 1.744}, {"sql": "select exists(select * from `states`) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 481}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 569}, {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Requests/CheckoutRequest.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Http\\Requests\\CheckoutRequest.php", "line": 40}, {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Requests/SaveCheckoutInformationRequest.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Http\\Requests\\SaveCheckoutInformationRequest.php", "line": 11}, {"index": 16, "namespace": null, "name": "platform/core/js-validation/src/JsValidatorFactory.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\js-validation\\src\\JsValidatorFactory.php", "line": 91}], "start": **********.056106, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "EcommerceHelper.php:481", "source": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php:481", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FEcommerceHelper.php&line=481", "ajax": false, "filename": "EcommerceHelper.php", "line": "481"}, "connection": "chubbybyp-v2", "start_percent": 22.158, "width_percent": 0.652}, {"sql": "select exists(select * from `countries`) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 480}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 1273}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 576}, {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Requests/CheckoutRequest.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Http\\Requests\\CheckoutRequest.php", "line": 40}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Requests/SaveCheckoutInformationRequest.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Http\\Requests\\SaveCheckoutInformationRequest.php", "line": 11}], "start": **********.060596, "duration": 0.003, "duration_str": "3ms", "memory": 0, "memory_str": null, "filename": "EcommerceHelper.php:480", "source": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php:480", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FEcommerceHelper.php&line=480", "ajax": false, "filename": "EcommerceHelper.php", "line": "480"}, "connection": "chubbybyp-v2", "start_percent": 22.81, "width_percent": 2.445}, {"sql": "select exists(select * from `states`) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 481}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 1273}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 576}, {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Requests/CheckoutRequest.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Http\\Requests\\CheckoutRequest.php", "line": 40}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Requests/SaveCheckoutInformationRequest.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Http\\Requests\\SaveCheckoutInformationRequest.php", "line": 11}], "start": **********.066386, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "EcommerceHelper.php:481", "source": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php:481", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FEcommerceHelper.php&line=481", "ajax": false, "filename": "EcommerceHelper.php", "line": "481"}, "connection": "chubbybyp-v2", "start_percent": 25.255, "width_percent": 1.206}, {"sql": "select exists(select * from `countries`) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 480}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 569}, {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Requests/CheckoutRequest.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Http\\Requests\\CheckoutRequest.php", "line": 68}, {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Requests/SaveCheckoutInformationRequest.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Http\\Requests\\SaveCheckoutInformationRequest.php", "line": 11}, {"index": 16, "namespace": null, "name": "platform/core/js-validation/src/JsValidatorFactory.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\js-validation\\src\\JsValidatorFactory.php", "line": 91}], "start": **********.074319, "duration": 0.03408, "duration_str": "34.08ms", "memory": 0, "memory_str": null, "filename": "EcommerceHelper.php:480", "source": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php:480", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FEcommerceHelper.php&line=480", "ajax": false, "filename": "EcommerceHelper.php", "line": "480"}, "connection": "chubbybyp-v2", "start_percent": 26.461, "width_percent": 27.773}, {"sql": "select exists(select * from `states`) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 481}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 569}, {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Requests/CheckoutRequest.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Http\\Requests\\CheckoutRequest.php", "line": 68}, {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Requests/SaveCheckoutInformationRequest.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Http\\Requests\\SaveCheckoutInformationRequest.php", "line": 11}, {"index": 16, "namespace": null, "name": "platform/core/js-validation/src/JsValidatorFactory.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\js-validation\\src\\JsValidatorFactory.php", "line": 91}], "start": **********.11065, "duration": 0.0029500000000000004, "duration_str": "2.95ms", "memory": 0, "memory_str": null, "filename": "EcommerceHelper.php:481", "source": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php:481", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FEcommerceHelper.php&line=481", "ajax": false, "filename": "EcommerceHelper.php", "line": "481"}, "connection": "chubbybyp-v2", "start_percent": 54.234, "width_percent": 2.404}, {"sql": "select exists(select * from `countries`) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 480}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 1273}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 576}, {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Requests/CheckoutRequest.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Http\\Requests\\CheckoutRequest.php", "line": 68}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Requests/SaveCheckoutInformationRequest.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Http\\Requests\\SaveCheckoutInformationRequest.php", "line": 11}], "start": **********.116668, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "EcommerceHelper.php:480", "source": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php:480", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FEcommerceHelper.php&line=480", "ajax": false, "filename": "EcommerceHelper.php", "line": "480"}, "connection": "chubbybyp-v2", "start_percent": 56.638, "width_percent": 0.848}, {"sql": "select exists(select * from `states`) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 481}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 1273}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 576}, {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Requests/CheckoutRequest.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Http\\Requests\\CheckoutRequest.php", "line": 68}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Requests/SaveCheckoutInformationRequest.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Http\\Requests\\SaveCheckoutInformationRequest.php", "line": 11}], "start": **********.1199741, "duration": 0.0029, "duration_str": "2.9ms", "memory": 0, "memory_str": null, "filename": "EcommerceHelper.php:481", "source": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php:481", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FEcommerceHelper.php&line=481", "ajax": false, "filename": "EcommerceHelper.php", "line": "481"}, "connection": "chubbybyp-v2", "start_percent": 57.485, "width_percent": 2.363}, {"sql": "select exists(select * from `countries`) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 480}, {"index": 13, "namespace": "view", "name": "plugins/ecommerce::orders.master", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform/plugins/ecommerce/resources/views/orders/master.blade.php", "line": 41}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 125}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.177293, "duration": 0.014039999999999999, "duration_str": "14.04ms", "memory": 0, "memory_str": null, "filename": "EcommerceHelper.php:480", "source": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php:480", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FEcommerceHelper.php&line=480", "ajax": false, "filename": "EcommerceHelper.php", "line": "480"}, "connection": "chubbybyp-v2", "start_percent": 59.848, "width_percent": 11.442}, {"sql": "select exists(select * from `states`) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 481}, {"index": 13, "namespace": "view", "name": "plugins/ecommerce::orders.master", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform/plugins/ecommerce/resources/views/orders/master.blade.php", "line": 41}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 125}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.193289, "duration": 0.03523, "duration_str": "35.23ms", "memory": 0, "memory_str": null, "filename": "EcommerceHelper.php:481", "source": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php:481", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FEcommerceHelper.php&line=481", "ajax": false, "filename": "EcommerceHelper.php", "line": "481"}, "connection": "chubbybyp-v2", "start_percent": 71.29, "width_percent": 28.71}]}, "models": {"data": {"Botble\\Location\\Models\\Country<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Flocation%2Fsrc%2FModels%2FCountry.php&line=1\" class=\"phpdebugbar-widgets-editor-link\"></a>": 250, "Botble\\Location\\Models\\City<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Flocation%2Fsrc%2FModels%2FCity.php&line=1\" class=\"phpdebugbar-widgets-editor-link\"></a>": 12, "Botble\\Ecommerce\\Models\\Currency<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FCurrency.php&line=1\" class=\"phpdebugbar-widgets-editor-link\"></a>": 9, "Botble\\Location\\Models\\State<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Flocation%2Fsrc%2FModels%2FState.php&line=1\" class=\"phpdebugbar-widgets-editor-link\"></a>": 7, "Botble\\Ecommerce\\Models\\Product<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=1\" class=\"phpdebugbar-widgets-editor-link\"></a>": 4, "Botble\\Ecommerce\\Models\\ProductVariation<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProductVariation.php&line=1\" class=\"phpdebugbar-widgets-editor-link\"></a>": 4, "Botble\\ACL\\Models\\User<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Facl%2Fsrc%2FModels%2FUser.php&line=1\" class=\"phpdebugbar-widgets-editor-link\"></a>": 1, "Botble\\Slug\\Models\\Slug<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fpackages%2Fslug%2Fsrc%2FModels%2FSlug.php&line=1\" class=\"phpdebugbar-widgets-editor-link\"></a>": 1, "Botble\\Ecommerce\\Models\\OrderAddress<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FOrderAddress.php&line=1\" class=\"phpdebugbar-widgets-editor-link\"></a>": 1, "Botble\\Ecommerce\\Models\\Shipping<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FShipping.php&line=1\" class=\"phpdebugbar-widgets-editor-link\"></a>": 1, "Botble\\Ecommerce\\Models\\ShippingRule<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FShippingRule.php&line=1\" class=\"phpdebugbar-widgets-editor-link\"></a>": 1}, "count": 291, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "jh1lEMPqUbufsTpHwVjiRRuAGAYe8v5WBn4lkaI4", "language": "en", "_previous": "array:1 [\n  \"url\" => \"https://chubbybyp-v2.gc/checkout/241afa5f1f6e9ca99f6cbf0bf30e3abd\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "cart": "array:2 [\n  \"cart_updated_at\" => Carbon\\Carbon @********** {#3685\n    #endOfTime: false\n    #startOfTime: false\n    #constructedObjectId: \"0000000000000e650000000000000000\"\n    #localMonthsOverflow: null\n    #localYearsOverflow: null\n    #localStrictModeEnabled: null\n    #localHumanDiffOptions: null\n    #localToStringFormat: null\n    #localSerializer: null\n    #localMacros: null\n    #localGenericMacros: null\n    #localFormatFunction: null\n    #localTranslator: null\n    #dumpProperties: array:3 [\n      0 => \"date\"\n      1 => \"timezone_type\"\n      2 => \"timezone\"\n    ]\n    #dumpLocale: null\n    #dumpDateProperties: null\n    date: 2025-07-30 21:53:27.217024 Asia/Dubai (+04:00)\n  }\n  \"cart\" => Illuminate\\Support\\Collection {#3686\n    #items: array:1 [\n      \"0ef574d36ebdc3a49943f0917f4f1fdb\" => Botble\\Ecommerce\\Cart\\CartItem {#4373\n        +rowId: \"0ef574d36ebdc3a49943f0917f4f1fdb\"\n        +id: 844\n        +qty: \"1\"\n        +name: \"Plus Size No Sleeve Ruffle Top\"\n        +price: 45.0\n        +options: Botble\\Ecommerce\\Cart\\CartItemOptions {#4322\n          #items: array:8 [\n            \"image\" => \"products/CHUBBY_BY_-_WBG-111__1_-removebg-preview.png\"\n            \"attributes\" => \"(Color: Black, Size: XXL/44)\"\n            \"taxRate\" => 0.0\n            \"taxClasses\" => []\n            \"options\" => []\n            \"extras\" => []\n            \"sku\" => \"00180\"\n            \"weight\" => 0.0\n          ]\n          #escapeWhenCastingToString: false\n        }\n        #associatedModel: null\n        #taxRate: 0.0\n        +\"created_at\": Carbon\\Carbon @********** {#4317\n          #endOfTime: false\n          #startOfTime: false\n          #constructedObjectId: \"00000000000010dd0000000000000000\"\n          #localMonthsOverflow: null\n          #localYearsOverflow: null\n          #localStrictModeEnabled: null\n          #localHumanDiffOptions: null\n          #localToStringFormat: null\n          #localSerializer: null\n          #localMacros: null\n          #localGenericMacros: null\n          #localFormatFunction: null\n          #localTranslator: null\n          #dumpProperties: array:3 [\n            0 => \"date\"\n            1 => \"timezone_type\"\n            2 => \"timezone\"\n          ]\n          #dumpLocale: null\n          #dumpDateProperties: null\n          date: 2025-07-30 21:53:27.216605 Asia/Dubai (+04:00)\n        }\n        +\"updated_at\": Carbon\\Carbon @********** {#4339\n          #endOfTime: false\n          #startOfTime: false\n          #constructedObjectId: \"00000000000010f30000000000000000\"\n          #localMonthsOverflow: null\n          #localYearsOverflow: null\n          #localStrictModeEnabled: null\n          #localHumanDiffOptions: null\n          #localToStringFormat: null\n          #localSerializer: null\n          #localMacros: null\n          #localGenericMacros: null\n          #localFormatFunction: null\n          #localTranslator: null\n          #dumpProperties: array:3 [\n            0 => \"date\"\n            1 => \"timezone_type\"\n            2 => \"timezone\"\n          ]\n          #dumpLocale: null\n          #dumpDateProperties: null\n          date: 2025-07-30 21:53:27.216852 Asia/Dubai (+04:00)\n        }\n      }\n    ]\n    #escapeWhenCastingToString: false\n  }\n]", "tracked_start_checkout": "241afa5f1f6e9ca99f6cbf0bf30e3abd", "d0048421b8a0e3557bda5c3fde284d0e": "array:20 [\n  \"promotion_discount_amount\" => 0\n  \"billing_address_same_as_shipping_address\" => true\n  \"billing_address\" => []\n  \"created_order\" => true\n  \"created_order_id\" => 179\n  \"is_save_order_shipping_address\" => true\n  \"created_order_product\" => Carbon\\Carbon @1753897842 {#3698\n    #endOfTime: false\n    #startOfTime: false\n    #constructedObjectId: \"0000000000000e720000000000000000\"\n    #localMonthsOverflow: null\n    #localYearsOverflow: null\n    #localStrictModeEnabled: null\n    #localHumanDiffOptions: null\n    #localToStringFormat: null\n    #localSerializer: null\n    #localMacros: null\n    #localGenericMacros: null\n    #localFormatFunction: null\n    #localTranslator: null\n    #dumpProperties: array:3 [\n      0 => \"date\"\n      1 => \"timezone_type\"\n      2 => \"timezone\"\n    ]\n    #dumpLocale: null\n    #dumpDateProperties: null\n    date: 2025-07-30 21:50:42.296475 Asia/Dubai (+04:00)\n  }\n  \"name\" => \"<PERSON>\"\n  \"email\" => \"<EMAIL>\"\n  \"phone\" => \"**********\"\n  \"address\" => \"26 Duncan St\"\n  \"zip_code\" => \"M5V 2B9\"\n  \"country\" => \"2\"\n  \"state\" => \"8\"\n  \"city\" => \"11\"\n  \"created_order_address\" => true\n  \"created_order_address_id\" => 92\n  \"shipping_method\" => \"default\"\n  \"shipping_option\" => \"9\"\n  \"shipping_amount\" => \"6.80\"\n]", "selected_payment_method": "bank_transfer"}, "request": {"path_info": "/checkout/241afa5f1f6e9ca99f6cbf0bf30e3abd", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jh1lEMPqUbufsTpHwVjiRRuAGAYe8v5WBn4lkaI4</span>\"\n  \"<span class=sf-dump-key>checkout-token</span>\" => \"<span class=sf-dump-str title=\"32 characters\">241afa5f1f6e9ca99f6cbf0bf30e3abd</span>\"\n  \"<span class=sf-dump-key>coupon_code</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>address</span>\" => <span class=sf-dump-note>array:8</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\"><PERSON></span>\"\n    \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"23 characters\"><EMAIL></span>\"\n    \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"10 characters\">**********</span>\"\n    \"<span class=sf-dump-key>address</span>\" => \"<span class=sf-dump-str title=\"12 characters\">26 Duncan St</span>\"\n    \"<span class=sf-dump-key>zip_code</span>\" => \"<span class=sf-dump-str title=\"7 characters\">M5V 2B9</span>\"\n    \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str>2</span>\"\n    \"<span class=sf-dump-key>state</span>\" => \"<span class=sf-dump-str>8</span>\"\n    \"<span class=sf-dump-key>city</span>\" => \"<span class=sf-dump-str title=\"2 characters\">11</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>password</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>password_confirmation</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>shipping_option</span>\" => \"<span class=sf-dump-str>9</span>\"\n  \"<span class=sf-dump-key>shipping_method</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n  \"<span class=sf-dump-key>amount</span>\" => \"<span class=sf-dump-str title=\"4 characters\">51.8</span>\"\n  \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">USD</span>\"\n  \"<span class=sf-dump-key>payment_method</span>\" => \"<span class=sf-dump-str title=\"13 characters\">bank_transfer</span>\"\n  \"<span class=sf-dump-key>description</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>tax_information</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>company_name</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>company_address</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>company_tax_code</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>company_email</span>\" => \"<span class=sf-dump-str title=\"28 characters\">payment_method=bank_transfer</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">chubbybyp-v2.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">text/html, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">https://chubbybyp-v2.gc/checkout/241afa5f1f6e9ca99f6cbf0bf30e3abd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2880 characters\">botble_footprints_cookie=eyJpdiI6IkZkR1A0Y29ydU9wSkpvdi9MdVk3UHc9PSIsInZhbHVlIjoiZE1HR0ZoQjdmTzV5OHNtaUFkaWNDR1l4OERXYldBWCs1YURGeXV4SmpBaldKSWJiT0NJVURReDlyNktldG0vRy8yN0NocFBqNmRBMXh4dVZRQVdyekVEQjdqRmlvSksxNGNVVGkzTmFZMEFXR3o4Mm96UXdtWTZ0YldpOS94UGoiLCJtYWMiOiJkMjdiZWUwMDlkNTVmYTIxYjM1NGQwYmMxZTY1ODg1ODgwODRhZjFkZGUzMGIzYjYyOWFmYTVlMDMyZTIyYzkxIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6Ik1oMEM3S3NpZWprSTY5RTVGOFFaQ3c9PSIsInZhbHVlIjoiNjc1eE1LTGZpaGsraVZ3QW13Q3JXaWhySXRUMkRDNERld0k3VzFwMFNheklvOEI5dkZFWjF3RDdMODFxVmttWE9UajJTOHl1eWlHTGV5eFVHYTYxaU5jZ3dQbVdaRlpXRDNZMGxYL242RVhQdDNTbmFsMVJ2RWgxdVN6aHdLS2xvQUV0U1YvNHF6a1lIL0FRa1ZMVEdnWEpHd3hUS1dvVURMOWIwWWRBTTNSbUVmRlVSY0VYUHVqOTg3RThJNXpHekpHZmtEK1Z1SWJhRUJpODZEQ1J0MzBoQmJ4ekZzRnJPdWM4aDZuM3ZZNjk4NkpJVFI4WGpVMnV5TTdTM3RVY3ZGTkUrWWJWTDRLZjVhODl5NW5iUFQ3dGlJRkVFUy9qcTJmK0YzS0MwZnRjbmxhOXFKWCtnazNzZXhFNHBNckZ4VXR2S0JsUjhnbTRqQ1R3OEd1RjRqMEp3cGExeU1Wa1k5WVp5SlB6ditjeGZjSFZBMyt0dFZZZFcya095b2pmM0FMWm5Yb0ZaMzM5cWE4YzlNV285RmVxTVg5TlBiYllxTkNBeGZ2MnUvQlBLY1h5a3ZJWi9nY1B1b1hUZWlMeWRXOG1HclJZVnk5cGdzMXp5N3FSTGE1SzV5Z1k5RS9CM1VwU2JKakt0czJuRklyODdPWUpiblovdWxMK045ZGJUd2FNVERGWmlnVmUxNldKZTZMSEtnPT0iLCJtYWMiOiJiYjUwN2IwMDY4OTJjY2JlNzQ1YWUwODIxYTVhMmUzZjBlYzQyZWFmMzQzNmQ4NzUwOGUxNTc4ODcxMjhiZTJhIiwidGFnIjoiIn0%3D; _gcl_au=1.1.1091535652.1753890285; _ga=GA1.1.1700318349.1753890285; _tt_enable_cookie=1; _ttp=01K1E03SZ79B2723EEJDJ4JGVK_.tt.1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im5pZnQ3WnIyeTZLbE42WVNEVGZUcFE9PSIsInZhbHVlIjoiTTllTlo2bGNYbTRod1NYTWJJQms0WVdLcXBWVVVQTjJlWFJUUXNFYzlVTy9qbFROTGdDeVNnRGdyYyswZC9oZkwraVRLcFNSUzUwdk04TWY2MGY3Z1lyL3FYdEx4eDQ4V0NNQUZDQnJ2MG84NVQ3UFpXNmtnWTBBT0luQjVXaDRoOUtPSjlIRXNFaVVqYmlvMTkxUm5Uc2hZRktCZVM3Yk1sdi9CTDBOUTRSck1LdHY2Vks0eThoY0lQdU1xcDEwLzJINzBoQ2JrbTRHVUVzTkhtaE1ZQjJUZlBFbmRCQ2tOZ0ZoNlFiVHZ5az0iLCJtYWMiOiIwY2EwZmVjNTAzYTBjNTY4NTIzYjcxNjA0OTc5YWYzMjk1OWM3YzY4NGY3NzRiNmQzNDkyMDU3NjI4ZWE2YTMzIiwidGFnIjoiIn0%3D; ttcsid=1753897739151::jrcBqMKvIJe7sED08Eew.2.1753897825781; ttcsid_CM0OVARC77UDNKHAHMLG=1753897739150::o8q2bNW2cww8z-WM1DwD.2.1753897838814; _ga_WDSYD0V4EF=GS2.1.s1753897738$o2$g1$t1753897850$j34$l0$h557063752; XSRF-TOKEN=eyJpdiI6Ikw5TFJ2bktLQ214RGlOcXNvVnpVUFE9PSIsInZhbHVlIjoiRHB2WSt1ZFhvNmZwK216eVNHZTVKWStacng0MjAyUE95aHFBVmQyUHVWQ1NzalVBTGFTRVRWM2VWbitlZHdFajJXTTdZZFIwdmoxVkRaQVFaZTA3NENvVjh3b2U2TmdpRGJvVktMazF4bzVzZjlVQ0RVYVAvNmZhYmh0dUN6aXciLCJtYWMiOiJjYjhiZDg3ZWMwNmVkNWEyODlhMDJmZGVkNmYyYjRmYzJiZjEyMWQ0YTI5NzVkZDAxN2NlYzI2MWJhMmY4YWQ0IiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6IjI4Um44V1JIN0NqYjMyeUVPSE9rdlE9PSIsInZhbHVlIjoibE9JalV4dnlRNzZHdWxuVy9mTnc3WWQ0VndleE00K1VoY0N3MElmcjlhallQclNFSXh1cUtHZGJnQTBhU291bTlPYVNmS3hJSFhtNDBZRVNlV0RXOVVDK3JhNkRobDhlZFpmVW9qZ0REeUYzOC9hQUJRUDFoVjRkYk5ONE5pOWciLCJtYWMiOiJkMmMyMjFkM2I5ZGQxNGI5Y2MyNDljZTkwNmU2YWI4NmQyN2UyNWM1ZDljYzJlZWIxZTM3M2EyNTNjYzNkYmQ1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">31bb862467c8e69b344d5c45026a75ed73ed5d26</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"351 characters\">{&quot;footprint&quot;:&quot;31bb862467c8e69b344d5c45026a75ed73ed5d26&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;chubbybyp-v2.gc&quot;,&quot;landing_page&quot;:&quot;\\/&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:&quot;http:\\/\\/localhost\\/&quot;,&quot;referrer_domain&quot;:&quot;localhost&quot;}</span>\"\n  \"<span class=sf-dump-key>_gcl_au</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_tt_enable_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ttp</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|AGlUzxciifw9JcFUPAL5OSaSqWH54hoxjvrpteKTZIiKkGVxMRd4jFgyKOSx|$2y$12$ou/bc46LY/TX4Ep5.Uq6GOtqAMuOXT6Ej7RDW9moFmk/a0wvYTZJa</span>\"\n  \"<span class=sf-dump-key>ttcsid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>ttcsid_CM0OVARC77UDNKHAHMLG</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_WDSYD0V4EF</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jh1lEMPqUbufsTpHwVjiRRuAGAYe8v5WBn4lkaI4</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JupNiy8yjG2B1llXoLp4Hx796gEZWY8Hc5s0vzYH</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1492432827 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 17:53:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"436 characters\">XSRF-TOKEN=eyJpdiI6Ild1ZVovUUd6dFo3eG5ObHVxeFBiMWc9PSIsInZhbHVlIjoiNUFOSlF5TlVNRjJsSHdZYkRiOEV1ZXdkN3huQ3BFdEdSK1JDcW5xL3dZcU1UVWJOaktMdTQ4d3hjay92N2FJY3VkdXp3Ry9VbmxiWGVra2RkR1hkSnNidHUrVDY4TjlJWUU3NFhQWkJZOWM4amY2NDZ0L1MwaDJUQ2h4VWIyWWMiLCJtYWMiOiJmMjRlYmFmNDU4NmJlM2U4NGNjZjE3ZmMzYzRkNDYwZDQ5MzQyMGIyMThjNDdhMzc5NTJjMTY2ZWJjMjgxYzBlIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 19:53:36 GMT; Max-Age=7200; path=/; secure; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"442 characters\">botble_session=eyJpdiI6IjNnUFZDenlOQTgzLy83dVZDd2h4Ync9PSIsInZhbHVlIjoiUTFYUVVkWG5EL1BhZ1c2Rlc5VXhWdlFzYVJBMWZ4K2UyVDdMSmhDV25NeitRNEkvdzRnRGRIeHo0S05ZbGRDYThIbnlNUHIrbVNzYys5ZGc3Ykp1clB5UHFpbGh4R2Z4MGc5TG51Mk5Qejdyakw0TDBsZXowSW1sWHQrakp2RmkiLCJtYWMiOiI1YmIwZDQ5MTc2MmYyYjM0MGQwYTkyM2MzMGIyMWU3NzQyMDI5MTIzZGVmMzcxMTE1ZGFmMDkzYmQ0NWRkODcxIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 19:53:36 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"408 characters\">XSRF-TOKEN=eyJpdiI6Ild1ZVovUUd6dFo3eG5ObHVxeFBiMWc9PSIsInZhbHVlIjoiNUFOSlF5TlVNRjJsSHdZYkRiOEV1ZXdkN3huQ3BFdEdSK1JDcW5xL3dZcU1UVWJOaktMdTQ4d3hjay92N2FJY3VkdXp3Ry9VbmxiWGVra2RkR1hkSnNidHUrVDY4TjlJWUU3NFhQWkJZOWM4amY2NDZ0L1MwaDJUQ2h4VWIyWWMiLCJtYWMiOiJmMjRlYmFmNDU4NmJlM2U4NGNjZjE3ZmMzYzRkNDYwZDQ5MzQyMGIyMThjNDdhMzc5NTJjMTY2ZWJjMjgxYzBlIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 19:53:36 GMT; path=/; secure</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"414 characters\">botble_session=eyJpdiI6IjNnUFZDenlOQTgzLy83dVZDd2h4Ync9PSIsInZhbHVlIjoiUTFYUVVkWG5EL1BhZ1c2Rlc5VXhWdlFzYVJBMWZ4K2UyVDdMSmhDV25NeitRNEkvdzRnRGRIeHo0S05ZbGRDYThIbnlNUHIrbVNzYys5ZGc3Ykp1clB5UHFpbGh4R2Z4MGc5TG51Mk5Qejdyakw0TDBsZXowSW1sWHQrakp2RmkiLCJtYWMiOiI1YmIwZDQ5MTc2MmYyYjM0MGQwYTkyM2MzMGIyMWU3NzQyMDI5MTIzZGVmMzcxMTE1ZGFmMDkzYmQ0NWRkODcxIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 19:53:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1492432827\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jh1lEMPqUbufsTpHwVjiRRuAGAYe8v5WBn4lkaI4</span>\"\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"65 characters\">https://chubbybyp-v2.gc/checkout/241afa5f1f6e9ca99f6cbf0bf30e3abd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>cart</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>cart_updated_at</span>\" => <span class=sf-dump-note title=\"Carbon\\Carbon @**********\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Carbon @**********</span> {<a class=sf-dump-ref>#3685</a><samp data-depth=3 class=sf-dump-compact>\n      #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n      #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n      #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">0000000000000e650000000000000000</span>\"\n      #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n        <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n      </samp>]\n      #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n      <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Wednesday, July 30, 2025\n- 00:00:09.039289 from now\nDST Off\">2025-07-30 21:53:27.217024 Asia/Dubai (+04:00)</span>\n    </samp>}\n    \"<span class=sf-dump-key>cart</span>\" => <span class=sf-dump-note title=\"Illuminate\\Support\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Collection</span> {<a class=sf-dump-ref>#3686</a><samp data-depth=3 class=sf-dump-compact>\n      #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>0ef574d36ebdc3a49943f0917f4f1fdb</span>\" => <span class=sf-dump-note title=\"Botble\\Ecommerce\\Cart\\CartItem\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Ecommerce\\Cart</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>CartItem</span> {<a class=sf-dump-ref>#4373</a><samp data-depth=5 class=sf-dump-compact>\n          +<span class=sf-dump-public title=\"Public property\">rowId</span>: \"<span class=sf-dump-str title=\"32 characters\">0ef574d36ebdc3a49943f0917f4f1fdb</span>\"\n          +<span class=sf-dump-public title=\"Public property\">id</span>: <span class=sf-dump-num>844</span>\n          +<span class=sf-dump-public title=\"Public property\">qty</span>: \"<span class=sf-dump-str>1</span>\"\n          +<span class=sf-dump-public title=\"Public property\">name</span>: \"<span class=sf-dump-str title=\"30 characters\">Plus Size No Sleeve Ruffle Top</span>\"\n          +<span class=sf-dump-public title=\"Public property\">price</span>: <span class=sf-dump-num>45.0</span>\n          +<span class=sf-dump-public title=\"Public property\">options</span>: <span class=sf-dump-note title=\"Botble\\Ecommerce\\Cart\\CartItemOptions\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Ecommerce\\Cart</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>CartItemOptions</span> {<a class=sf-dump-ref>#4322</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"53 characters\">products/CHUBBY_BY_-_WBG-111__1_-removebg-preview.png</span>\"\n              \"<span class=sf-dump-key>attributes</span>\" => \"<span class=sf-dump-str title=\"28 characters\">(Color: Black, Size: XXL/44)</span>\"\n              \"<span class=sf-dump-key>taxRate</span>\" => <span class=sf-dump-num>0.0</span>\n              \"<span class=sf-dump-key>taxClasses</span>\" => []\n              \"<span class=sf-dump-key>options</span>\" => []\n              \"<span class=sf-dump-key>extras</span>\" => []\n              \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"5 characters\">00180</span>\"\n              \"<span class=sf-dump-key>weight</span>\" => <span class=sf-dump-num>0.0</span>\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          </samp>}\n          #<span class=sf-dump-protected title=\"Protected property\">associatedModel</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">taxRate</span>: <span class=sf-dump-num>0.0</span>\n          +\"<span class=sf-dump-public title=\"Runtime added dynamic property\">created_at</span>\": <span class=sf-dump-note title=\"Carbon\\Carbon @**********\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Carbon @**********</span> {<a class=sf-dump-ref>#4317</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">00000000000010dd0000000000000000</span>\"\n            #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n            <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Wednesday, July 30, 2025\n- 00:00:09.040021 from now\nDST Off\">2025-07-30 21:53:27.216605 Asia/Dubai (+04:00)</span>\n          </samp>}\n          +\"<span class=sf-dump-public title=\"Runtime added dynamic property\">updated_at</span>\": <span class=sf-dump-note title=\"Carbon\\Carbon @**********\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Carbon @**********</span> {<a class=sf-dump-ref>#4339</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">00000000000010f30000000000000000</span>\"\n            #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n            <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Wednesday, July 30, 2025\n- 00:00:09.039842 from now\nDST Off\">2025-07-30 21:53:27.216852 Asia/Dubai (+04:00)</span>\n          </samp>}\n        </samp>}\n      </samp>]\n      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n    </samp>}\n  </samp>]\n  \"<span class=sf-dump-key>tracked_start_checkout</span>\" => \"<span class=sf-dump-str title=\"32 characters\">241afa5f1f6e9ca99f6cbf0bf30e3abd</span>\"\n  \"<span class=sf-dump-key>d0048421b8a0e3557bda5c3fde284d0e</span>\" => <span class=sf-dump-note>array:20</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>promotion_discount_amount</span>\" => <span class=sf-dump-num>0</span>\n    \"<span class=sf-dump-key>billing_address_same_as_shipping_address</span>\" => <span class=sf-dump-const>true</span>\n    \"<span class=sf-dump-key>billing_address</span>\" => []\n    \"<span class=sf-dump-key>created_order</span>\" => <span class=sf-dump-const>true</span>\n    \"<span class=sf-dump-key>created_order_id</span>\" => <span class=sf-dump-num>179</span>\n    \"<span class=sf-dump-key>is_save_order_shipping_address</span>\" => <span class=sf-dump-const>true</span>\n    \"<span class=sf-dump-key>created_order_product</span>\" => <span class=sf-dump-note title=\"Carbon\\Carbon @1753897842\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Carbon @1753897842</span> {<a class=sf-dump-ref>#3698</a><samp data-depth=3 class=sf-dump-compact>\n      #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n      #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n      #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">0000000000000e720000000000000000</span>\"\n      #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n        <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n      </samp>]\n      #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n      <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Wednesday, July 30, 2025\n- 00:02:53.959967 from now\nDST Off\">2025-07-30 21:50:42.296475 Asia/Dubai (+04:00)</span>\n    </samp>}\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Alex Fleming</span>\"\n    \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"23 characters\"><EMAIL></span>\"\n    \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"10 characters\">**********</span>\"\n    \"<span class=sf-dump-key>address</span>\" => \"<span class=sf-dump-str title=\"12 characters\">26 Duncan St</span>\"\n    \"<span class=sf-dump-key>zip_code</span>\" => \"<span class=sf-dump-str title=\"7 characters\">M5V 2B9</span>\"\n    \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str>2</span>\"\n    \"<span class=sf-dump-key>state</span>\" => \"<span class=sf-dump-str>8</span>\"\n    \"<span class=sf-dump-key>city</span>\" => \"<span class=sf-dump-str title=\"2 characters\">11</span>\"\n    \"<span class=sf-dump-key>created_order_address</span>\" => <span class=sf-dump-const>true</span>\n    \"<span class=sf-dump-key>created_order_address_id</span>\" => <span class=sf-dump-num>92</span>\n    \"<span class=sf-dump-key>shipping_method</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n    \"<span class=sf-dump-key>shipping_option</span>\" => \"<span class=sf-dump-str>9</span>\"\n    \"<span class=sf-dump-key>shipping_amount</span>\" => \"<span class=sf-dump-str title=\"4 characters\">6.80</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>selected_payment_method</span>\" => \"<span class=sf-dump-str title=\"13 characters\">bank_transfer</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}