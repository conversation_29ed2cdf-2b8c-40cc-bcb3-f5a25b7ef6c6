{"__meta": {"id": "X57bf5d9ccddac6f0bbf2d91ff662b295", "datetime": "2025-07-30 21:51:54", "utime": 1753897914.593485, "method": "POST", "uri": "/checkout/241afa5f1f6e9ca99f6cbf0bf30e3abd/process", "ip": "127.0.0.1"}, "php": {"version": "8.3.17", "interface": "apache2handler"}, "messages": {"count": 2, "messages": [{"message": "[21:51:53] LOG.warning: Creation of dynamic property Botble\\Ecommerce\\Cart\\CartItem::$created_at is deprecated in D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php on line 117", "message_html": null, "is_string": false, "label": "warning", "time": 1753897913.256338, "xdebug_link": null, "collector": "log"}, {"message": "[21:51:53] LOG.warning: Creation of dynamic property Botble\\Ecommerce\\Cart\\CartItem::$updated_at is deprecated in D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php on line 117", "message_html": null, "is_string": false, "label": "warning", "time": 1753897913.256495, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753897912.50669, "end": 1753897914.593511, "duration": 2.0868210792541504, "duration_str": "2.09s", "measures": [{"label": "Booting", "start": 1753897912.50669, "relative_start": 0, "end": 1753897913.219732, "relative_end": 1753897913.219732, "duration": 0.7130420207977295, "duration_str": "713ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753897913.219746, "relative_start": 0.7130560874938965, "end": 1753897914.593514, "relative_end": 2.86102294921875e-06, "duration": 1.3737678527832031, "duration_str": "1.37s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45918744, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST checkout/{token}/process", "middleware": "web, core, localeSessionRedirect, localizationRedirect", "as": "public.checkout.process", "controller": "Botble\\Ecommerce\\Http\\Controllers\\Fronts\\PublicCheckoutController@postCheckout", "namespace": "Botble\\Ecommerce\\Http\\Controllers\\Fronts", "prefix": "/checkout/{token}", "where": [], "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FHttp%2FControllers%2FFronts%2FPublicCheckoutController.php&line=578\" onclick=\"\">platform/plugins/ecommerce/src/Http/Controllers/Fronts/PublicCheckoutController.php:578-854</a>"}, "queries": {"nb_statements": 22, "nb_failed_statements": 0, "accumulated_duration": 0.011159999999999998, "accumulated_duration_str": "11.16ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `ec_currencies` order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/CurrencySupport.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\CurrencySupport.php", "line": 109}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/CurrencySupport.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\CurrencySupport.php", "line": 43}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/helpers/currencies.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\helpers\\currencies.php", "line": 141}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 88}], "start": 1753897913.273392, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:55", "source": "platform/core/base/src/Models/BaseQueryBuilder.php:55", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=55", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "55"}, "connection": "chubbybyp-v2", "start_percent": 0, "width_percent": 4.839}, {"sql": "select distinct `ec_products`.*, `products_with_final_price`.`final_price` from `ec_products` inner join\n(\nSELECT DISTINCT\nec_products.id,\nCASE\nWHEN (\nec_products.sale_type = 0 AND\nec_products.sale_price <> 0\n) THEN ec_products.sale_price\nWHEN (\nec_products.sale_type = 0 AND\nec_products.sale_price = 0\n) THEN ec_products.price\nWHEN (\nec_products.sale_type = 1 AND\n(\nec_products.start_date > '2025-07-30 21:51:54' OR\nec_products.end_date < '2025-07-30 21:51:54'\n)\n) THEN ec_products.price\nWHEN (\nec_products.sale_type = 1 AND\nec_products.start_date <= '2025-07-30 21:51:54' AND\nec_products.end_date >= '2025-07-30 21:51:54'\n) THEN ec_products.sale_price\nWHEN (\nec_products.sale_type = 1 AND\nec_products.start_date IS NULL AND\nec_products.end_date >= '2025-07-30 21:51:54'\n) THEN ec_products.sale_price\nWHEN (\nec_products.sale_type = 1 AND\nec_products.start_date <= '2025-07-30 21:51:54' AND\nec_products.end_date IS NULL\n) THEN ec_products.sale_price\nELSE ec_products.price\nEND AS final_price\nFROM ec_products\n) AS products_with_final_price\non `products_with_final_price`.`id` = `ec_products`.`id` where `status` = 'published' and `ec_products`.`id` in (844) order by `order` asc, `created_at` desc", "type": "query", "params": [], "bindings": ["published", "844"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 16, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 370}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 671}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 88}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 717}], "start": 1753897914.33941, "duration": 0.00188, "duration_str": "1.88ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:55", "source": "platform/core/base/src/Models/BaseQueryBuilder.php:55", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=55", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "55"}, "connection": "chubbybyp-v2", "start_percent": 4.839, "width_percent": 16.846}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (844) and `slugs`.`reference_type` = 'Botble\\Ecommerce\\Models\\Product'", "type": "query", "params": [], "bindings": ["Botble\\Ecommerce\\Models\\Product"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 22, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 370}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 671}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 88}], "start": 1753897914.346262, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:55", "source": "platform/core/base/src/Models/BaseQueryBuilder.php:55", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=55", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "55"}, "connection": "chubbybyp-v2", "start_percent": 21.685, "width_percent": 3.584}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`is_default` = 1 and `ec_product_variations`.`configurable_product_id` in (844)", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 22, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 370}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 671}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 88}], "start": 1753897914.3501492, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:55", "source": "platform/core/base/src/Models/BaseQueryBuilder.php:55", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=55", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "55"}, "connection": "chubbybyp-v2", "start_percent": 25.269, "width_percent": 4.301}, {"sql": "select `ec_product_collections`.*, `ec_product_collection_products`.`product_id` as `pivot_product_id`, `ec_product_collection_products`.`product_collection_id` as `pivot_product_collection_id` from `ec_product_collections` inner join `ec_product_collection_products` on `ec_product_collections`.`id` = `ec_product_collection_products`.`product_collection_id` where `ec_product_collection_products`.`product_id` in (844)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 20, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 370}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 671}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 88}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 717}], "start": 1753897914.357746, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:55", "source": "platform/core/base/src/Models/BaseQueryBuilder.php:55", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=55", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "55"}, "connection": "chubbybyp-v2", "start_percent": 29.57, "width_percent": 4.928}, {"sql": "select `ec_product_labels`.*, `ec_product_label_products`.`product_id` as `pivot_product_id`, `ec_product_label_products`.`product_label_id` as `pivot_product_label_id` from `ec_product_labels` inner join `ec_product_label_products` on `ec_product_labels`.`id` = `ec_product_label_products`.`product_label_id` where `ec_product_label_products`.`product_id` in (844)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 20, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 370}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 671}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 88}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 717}], "start": 1753897914.360144, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:55", "source": "platform/core/base/src/Models/BaseQueryBuilder.php:55", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=55", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "55"}, "connection": "chubbybyp-v2", "start_percent": 34.498, "width_percent": 3.763}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`product_id` in (844)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 22, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 370}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 671}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 88}], "start": 1753897914.362468, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:55", "source": "platform/core/base/src/Models/BaseQueryBuilder.php:55", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=55", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "55"}, "connection": "chubbybyp-v2", "start_percent": 38.262, "width_percent": 3.315}, {"sql": "select * from `ec_products` where `ec_products`.`id` in (808)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 27, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 28, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 370}, {"index": 29, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 671}], "start": 1753897914.364469, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:55", "source": "platform/core/base/src/Models/BaseQueryBuilder.php:55", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=55", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "55"}, "connection": "chubbybyp-v2", "start_percent": 41.577, "width_percent": 4.211}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (808) and `slugs`.`reference_type` = 'Botble\\Ecommerce\\Models\\Product'", "type": "query", "params": [], "bindings": ["Botble\\Ecommerce\\Models\\Product"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 27, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 33, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 34, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 370}], "start": 1753897914.366461, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:55", "source": "platform/core/base/src/Models/BaseQueryBuilder.php:55", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=55", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "55"}, "connection": "chubbybyp-v2", "start_percent": 45.789, "width_percent": 4.301}, {"sql": "select distinct `ec_product_variations`.`product_id`, `ec_product_variations`.`configurable_product_id`, `ec_product_attributes`.*, `ec_product_attribute_sets`.`title` as `attribute_set_title`, `ec_product_attribute_sets`.`slug` as `attribute_set_slug` from `ec_product_variations` inner join `ec_product_variation_items` on `ec_product_variation_items`.`variation_id` = `ec_product_variations`.`id` inner join `ec_product_attributes` on `ec_product_attributes`.`id` = `ec_product_variation_items`.`attribute_id` inner join `ec_product_attribute_sets` on `ec_product_attribute_sets`.`id` = `ec_product_attributes`.`attribute_set_id` where `ec_product_variations`.`product_id` in (844) order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 22, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 370}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 671}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 88}], "start": 1753897914.371815, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:55", "source": "platform/core/base/src/Models/BaseQueryBuilder.php:55", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=55", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "55"}, "connection": "chubbybyp-v2", "start_percent": 50.09, "width_percent": 5.466}, {"sql": "select exists(select * from `countries`) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 480}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 179}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 546}, {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 565}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Requests/CheckoutRequest.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Http\\Requests\\CheckoutRequest.php", "line": 40}], "start": 1753897914.390382, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "EcommerceHelper.php:480", "source": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php:480", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FEcommerceHelper.php&line=480", "ajax": false, "filename": "EcommerceHelper.php", "line": "480"}, "connection": "chubbybyp-v2", "start_percent": 55.556, "width_percent": 4.48}, {"sql": "select exists(select * from `states`) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 481}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 179}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 546}, {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 565}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Requests/CheckoutRequest.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Http\\Requests\\CheckoutRequest.php", "line": 40}], "start": 1753897914.392155, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "EcommerceHelper.php:481", "source": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php:481", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FEcommerceHelper.php&line=481", "ajax": false, "filename": "EcommerceHelper.php", "line": "481"}, "connection": "chubbybyp-v2", "start_percent": 60.036, "width_percent": 3.853}, {"sql": "select `name`, `id` from `countries` where `status` = 'published' order by `order` asc, `name` asc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 185}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 546}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 565}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Requests/CheckoutRequest.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Http\\Requests\\CheckoutRequest.php", "line": 40}], "start": 1753897914.393828, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:55", "source": "platform/core/base/src/Models/BaseQueryBuilder.php:55", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=55", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "55"}, "connection": "chubbybyp-v2", "start_percent": 63.889, "width_percent": 6.9}, {"sql": "select exists(select * from `countries`) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 480}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 569}, {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Requests/CheckoutRequest.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Http\\Requests\\CheckoutRequest.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": 1753897914.5635688, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "EcommerceHelper.php:480", "source": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php:480", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FEcommerceHelper.php&line=480", "ajax": false, "filename": "EcommerceHelper.php", "line": "480"}, "connection": "chubbybyp-v2", "start_percent": 70.789, "width_percent": 3.584}, {"sql": "select exists(select * from `states`) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 481}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 569}, {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Requests/CheckoutRequest.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Http\\Requests\\CheckoutRequest.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": 1753897914.565016, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "EcommerceHelper.php:481", "source": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php:481", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FEcommerceHelper.php&line=481", "ajax": false, "filename": "EcommerceHelper.php", "line": "481"}, "connection": "chubbybyp-v2", "start_percent": 74.373, "width_percent": 2.509}, {"sql": "select exists(select * from `countries`) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 480}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 1273}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 576}, {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Requests/CheckoutRequest.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Http\\Requests\\CheckoutRequest.php", "line": 40}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": 1753897914.566916, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "EcommerceHelper.php:480", "source": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php:480", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FEcommerceHelper.php&line=480", "ajax": false, "filename": "EcommerceHelper.php", "line": "480"}, "connection": "chubbybyp-v2", "start_percent": 76.882, "width_percent": 2.599}, {"sql": "select exists(select * from `states`) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 481}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 1273}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 576}, {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Requests/CheckoutRequest.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Http\\Requests\\CheckoutRequest.php", "line": 40}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": 1753897914.568017, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "EcommerceHelper.php:481", "source": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php:481", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FEcommerceHelper.php&line=481", "ajax": false, "filename": "EcommerceHelper.php", "line": "481"}, "connection": "chubbybyp-v2", "start_percent": 79.48, "width_percent": 2.778}, {"sql": "select exists(select * from `countries`) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 480}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 569}, {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Requests/CheckoutRequest.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Http\\Requests\\CheckoutRequest.php", "line": 68}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": 1753897914.5703828, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "EcommerceHelper.php:480", "source": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php:480", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FEcommerceHelper.php&line=480", "ajax": false, "filename": "EcommerceHelper.php", "line": "480"}, "connection": "chubbybyp-v2", "start_percent": 82.258, "width_percent": 3.495}, {"sql": "select exists(select * from `states`) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 481}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 569}, {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Requests/CheckoutRequest.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Http\\Requests\\CheckoutRequest.php", "line": 68}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": 1753897914.571716, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "EcommerceHelper.php:481", "source": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php:481", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FEcommerceHelper.php&line=481", "ajax": false, "filename": "EcommerceHelper.php", "line": "481"}, "connection": "chubbybyp-v2", "start_percent": 85.753, "width_percent": 2.419}, {"sql": "select exists(select * from `countries`) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 480}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 1273}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 576}, {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Requests/CheckoutRequest.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Http\\Requests\\CheckoutRequest.php", "line": 68}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": 1753897914.572983, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "EcommerceHelper.php:480", "source": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php:480", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FEcommerceHelper.php&line=480", "ajax": false, "filename": "EcommerceHelper.php", "line": "480"}, "connection": "chubbybyp-v2", "start_percent": 88.172, "width_percent": 2.778}, {"sql": "select exists(select * from `states`) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 481}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 1273}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 576}, {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Requests/CheckoutRequest.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Http\\Requests\\CheckoutRequest.php", "line": 68}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": 1753897914.574198, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "EcommerceHelper.php:481", "source": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php:481", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FEcommerceHelper.php&line=481", "ajax": false, "filename": "EcommerceHelper.php", "line": "481"}, "connection": "chubbybyp-v2", "start_percent": 90.95, "width_percent": 2.867}, {"sql": "select exists(select * from `states` where (`id` = '8' and `status` = 'published' and `country_id` = '2')) as `exists`", "type": "query", "params": [], "bindings": ["8", "published", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/location/src/Rules/StateRule.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\location\\src\\Rules\\StateRule.php", "line": 37}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 861}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 650}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 457}, {"index": 15, "namespace": null, "name": "platform/core/js-validation/src/Remote/Validator.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\js-validation\\src\\Remote\\Validator.php", "line": 60}], "start": 1753897914.585984, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "StateRule.php:37", "source": "platform/plugins/location/src/Rules/StateRule.php:37", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Flocation%2Fsrc%2FRules%2FStateRule.php&line=37", "ajax": false, "filename": "StateRule.php", "line": "37"}, "connection": "chubbybyp-v2", "start_percent": 93.817, "width_percent": 6.183}]}, "models": {"data": {"Botble\\Location\\Models\\Country<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Flocation%2Fsrc%2FModels%2FCountry.php&line=1\" class=\"phpdebugbar-widgets-editor-link\"></a>": 250, "Botble\\Ecommerce\\Models\\Currency<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FCurrency.php&line=1\" class=\"phpdebugbar-widgets-editor-link\"></a>": 9, "Botble\\Ecommerce\\Models\\ProductVariation<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProductVariation.php&line=1\" class=\"phpdebugbar-widgets-editor-link\"></a>": 3, "Botble\\Ecommerce\\Models\\Product<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=1\" class=\"phpdebugbar-widgets-editor-link\"></a>": 2, "Botble\\Slug\\Models\\Slug<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fpackages%2Fslug%2Fsrc%2FModels%2FSlug.php&line=1\" class=\"phpdebugbar-widgets-editor-link\"></a>": 1}, "count": 265, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "jh1lEMPqUbufsTpHwVjiRRuAGAYe8v5WBn4lkaI4", "language": "en", "_previous": "array:1 [\n  \"url\" => \"https://chubbybyp-v2.gc/checkout/241afa5f1f6e9ca99f6cbf0bf30e3abd\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "cart": "array:2 [\n  \"cart_updated_at\" => Carbon\\Carbon @1753897882 {#3663\n    #endOfTime: false\n    #startOfTime: false\n    #constructedObjectId: \"0000000000000e4f0000000000000000\"\n    #localMonthsOverflow: null\n    #localYearsOverflow: null\n    #localStrictModeEnabled: null\n    #localHumanDiffOptions: null\n    #localToStringFormat: null\n    #localSerializer: null\n    #localMacros: null\n    #localGenericMacros: null\n    #localFormatFunction: null\n    #localTranslator: null\n    #dumpProperties: array:3 [\n      0 => \"date\"\n      1 => \"timezone_type\"\n      2 => \"timezone\"\n    ]\n    #dumpLocale: null\n    #dumpDateProperties: null\n    date: 2025-07-30 21:51:22.176601 Asia/Dubai (+04:00)\n  }\n  \"cart\" => Illuminate\\Support\\Collection {#3664\n    #items: array:1 [\n      \"0ef574d36ebdc3a49943f0917f4f1fdb\" => Botble\\Ecommerce\\Cart\\CartItem {#3665\n        +rowId: \"0ef574d36ebdc3a49943f0917f4f1fdb\"\n        +id: 844\n        +qty: \"1\"\n        +name: \"Plus Size No Sleeve Ruffle Top\"\n        +price: 45.0\n        +options: Botble\\Ecommerce\\Cart\\CartItemOptions {#3666\n          #items: array:8 [\n            \"image\" => \"products/CHUBBY_BY_-_WBG-111__1_-removebg-preview.png\"\n            \"attributes\" => \"(Color: Black, Size: XXL/44)\"\n            \"taxRate\" => 0.0\n            \"taxClasses\" => []\n            \"options\" => []\n            \"extras\" => []\n            \"sku\" => \"00180\"\n            \"weight\" => 0.0\n          ]\n          #escapeWhenCastingToString: false\n        }\n        #associatedModel: null\n        #taxRate: 0.0\n        +\"created_at\": Carbon\\Carbon @1753897882 {#3672\n          #endOfTime: false\n          #startOfTime: false\n          #constructedObjectId: \"0000000000000e580000000000000000\"\n          #localMonthsOverflow: null\n          #localYearsOverflow: null\n          #localStrictModeEnabled: null\n          #localHumanDiffOptions: null\n          #localToStringFormat: null\n          #localSerializer: null\n          #localMacros: null\n          #localGenericMacros: null\n          #localFormatFunction: null\n          #localTranslator: null\n          #dumpProperties: array:3 [\n            0 => \"date\"\n            1 => \"timezone_type\"\n            2 => \"timezone\"\n          ]\n          #dumpLocale: null\n          #dumpDateProperties: null\n          date: 2025-07-30 21:51:22.176110 Asia/Dubai (+04:00)\n        }\n        +\"updated_at\": Carbon\\Carbon @1753897882 {#3674\n          #endOfTime: false\n          #startOfTime: false\n          #constructedObjectId: \"0000000000000e5a0000000000000000\"\n          #localMonthsOverflow: null\n          #localYearsOverflow: null\n          #localStrictModeEnabled: null\n          #localHumanDiffOptions: null\n          #localToStringFormat: null\n          #localSerializer: null\n          #localMacros: null\n          #localGenericMacros: null\n          #localFormatFunction: null\n          #localTranslator: null\n          #dumpProperties: array:3 [\n            0 => \"date\"\n            1 => \"timezone_type\"\n            2 => \"timezone\"\n          ]\n          #dumpLocale: null\n          #dumpDateProperties: null\n          date: 2025-07-30 21:51:22.176431 Asia/Dubai (+04:00)\n        }\n      }\n    ]\n    #escapeWhenCastingToString: false\n  }\n]", "tracked_start_checkout": "241afa5f1f6e9ca99f6cbf0bf30e3abd", "d0048421b8a0e3557bda5c3fde284d0e": "array:15 [\n  \"promotion_discount_amount\" => 0\n  \"billing_address_same_as_shipping_address\" => true\n  \"billing_address\" => []\n  \"created_order\" => true\n  \"created_order_id\" => 179\n  \"is_save_order_shipping_address\" => true\n  \"created_order_product\" => Carbon\\Carbon @1753897842 {#3676\n    #endOfTime: false\n    #startOfTime: false\n    #constructedObjectId: \"0000000000000e5c0000000000000000\"\n    #localMonthsOverflow: null\n    #localYearsOverflow: null\n    #localStrictModeEnabled: null\n    #localHumanDiffOptions: null\n    #localToStringFormat: null\n    #localSerializer: null\n    #localMacros: null\n    #localGenericMacros: null\n    #localFormatFunction: null\n    #localTranslator: null\n    #dumpProperties: array:3 [\n      0 => \"date\"\n      1 => \"timezone_type\"\n      2 => \"timezone\"\n    ]\n    #dumpLocale: null\n    #dumpDateProperties: null\n    date: 2025-07-30 21:50:42.296475 Asia/Dubai (+04:00)\n  }\n  \"name\" => null\n  \"email\" => null\n  \"phone\" => null\n  \"address\" => null\n  \"zip_code\" => \"admin\"\n  \"country\" => null\n  \"state\" => null\n  \"city\" => null\n]", "selected_payment_method": "cod"}, "request": {"path_info": "/checkout/241afa5f1f6e9ca99f6cbf0bf30e3abd/process", "status_code": "<pre class=sf-dump id=sf-dump-1464141942 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1464141942\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-114697868 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-114697868\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jh1lEMPqUbufsTpHwVjiRRuAGAYe8v5WBn4lkaI4</span>\"\n  \"<span class=sf-dump-key>checkout-token</span>\" => \"<span class=sf-dump-str title=\"32 characters\">241afa5f1f6e9ca99f6cbf0bf30e3abd</span>\"\n  \"<span class=sf-dump-key>coupon_code</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>address</span>\" => <span class=sf-dump-note>array:8</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\"><PERSON></span>\"\n    \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"23 characters\"><EMAIL></span>\"\n    \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"10 characters\">6477384549</span>\"\n    \"<span class=sf-dump-key>address</span>\" => \"<span class=sf-dump-str title=\"12 characters\">26 Duncan St</span>\"\n    \"<span class=sf-dump-key>zip_code</span>\" => \"<span class=sf-dump-str title=\"7 characters\">M5V 2B9</span>\"\n    \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str>2</span>\"\n    \"<span class=sf-dump-key>state</span>\" => \"<span class=sf-dump-str>8</span>\"\n    \"<span class=sf-dump-key>city</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>password_confirmation</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>amount</span>\" => \"<span class=sf-dump-str title=\"2 characters\">45</span>\"\n  \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">USD</span>\"\n  \"<span class=sf-dump-key>payment_method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">cod</span>\"\n  \"<span class=sf-dump-key>description</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>tax_information</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>company_name</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>company_address</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>company_tax_code</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>company_email</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>_js_validation</span>\" => \"<span class=sf-dump-str title=\"14 characters\">address[state]</span>\"\n  \"<span class=sf-dump-key>_js_validation_validate_all</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1352765783 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">chubbybyp-v2.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">665</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"288 characters\">eyJpdiI6Ik0vSkt2RVVTQmQvbmVIZVVRRHRXVWc9PSIsInZhbHVlIjoic2dCZllicnZlL3FsY1B3TkRzOC9JNk5KVkVDdDdQNWJOVmRRK2pNNEg1cm90MEp4OTR5Z1Vub3VKa1VmQ2dwSkRIRW5ycklaMHlLODRldDZYVzRKMkE9PSIsIm1hYyI6Ijc1ZTA1NDU1ZWJkZWQyNTY5NjYwNGNiYzY3YWIzM2EwMmQ2N2Y0NTkzZmQ5MGJlMzQxY2JiNGM1MzExYTFiY2YiLCJ0YWciOiIifQ==</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">https://chubbybyp-v2.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">https://chubbybyp-v2.gc/checkout/241afa5f1f6e9ca99f6cbf0bf30e3abd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2880 characters\">botble_footprints_cookie=eyJpdiI6IkZkR1A0Y29ydU9wSkpvdi9MdVk3UHc9PSIsInZhbHVlIjoiZE1HR0ZoQjdmTzV5OHNtaUFkaWNDR1l4OERXYldBWCs1YURGeXV4SmpBaldKSWJiT0NJVURReDlyNktldG0vRy8yN0NocFBqNmRBMXh4dVZRQVdyekVEQjdqRmlvSksxNGNVVGkzTmFZMEFXR3o4Mm96UXdtWTZ0YldpOS94UGoiLCJtYWMiOiJkMjdiZWUwMDlkNTVmYTIxYjM1NGQwYmMxZTY1ODg1ODgwODRhZjFkZGUzMGIzYjYyOWFmYTVlMDMyZTIyYzkxIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6Ik1oMEM3S3NpZWprSTY5RTVGOFFaQ3c9PSIsInZhbHVlIjoiNjc1eE1LTGZpaGsraVZ3QW13Q3JXaWhySXRUMkRDNERld0k3VzFwMFNheklvOEI5dkZFWjF3RDdMODFxVmttWE9UajJTOHl1eWlHTGV5eFVHYTYxaU5jZ3dQbVdaRlpXRDNZMGxYL242RVhQdDNTbmFsMVJ2RWgxdVN6aHdLS2xvQUV0U1YvNHF6a1lIL0FRa1ZMVEdnWEpHd3hUS1dvVURMOWIwWWRBTTNSbUVmRlVSY0VYUHVqOTg3RThJNXpHekpHZmtEK1Z1SWJhRUJpODZEQ1J0MzBoQmJ4ekZzRnJPdWM4aDZuM3ZZNjk4NkpJVFI4WGpVMnV5TTdTM3RVY3ZGTkUrWWJWTDRLZjVhODl5NW5iUFQ3dGlJRkVFUy9qcTJmK0YzS0MwZnRjbmxhOXFKWCtnazNzZXhFNHBNckZ4VXR2S0JsUjhnbTRqQ1R3OEd1RjRqMEp3cGExeU1Wa1k5WVp5SlB6ditjeGZjSFZBMyt0dFZZZFcya095b2pmM0FMWm5Yb0ZaMzM5cWE4YzlNV285RmVxTVg5TlBiYllxTkNBeGZ2MnUvQlBLY1h5a3ZJWi9nY1B1b1hUZWlMeWRXOG1HclJZVnk5cGdzMXp5N3FSTGE1SzV5Z1k5RS9CM1VwU2JKakt0czJuRklyODdPWUpiblovdWxMK045ZGJUd2FNVERGWmlnVmUxNldKZTZMSEtnPT0iLCJtYWMiOiJiYjUwN2IwMDY4OTJjY2JlNzQ1YWUwODIxYTVhMmUzZjBlYzQyZWFmMzQzNmQ4NzUwOGUxNTc4ODcxMjhiZTJhIiwidGFnIjoiIn0%3D; _gcl_au=1.1.1091535652.1753890285; _ga=GA1.1.1700318349.1753890285; _tt_enable_cookie=1; _ttp=01K1E03SZ79B2723EEJDJ4JGVK_.tt.1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im5pZnQ3WnIyeTZLbE42WVNEVGZUcFE9PSIsInZhbHVlIjoiTTllTlo2bGNYbTRod1NYTWJJQms0WVdLcXBWVVVQTjJlWFJUUXNFYzlVTy9qbFROTGdDeVNnRGdyYyswZC9oZkwraVRLcFNSUzUwdk04TWY2MGY3Z1lyL3FYdEx4eDQ4V0NNQUZDQnJ2MG84NVQ3UFpXNmtnWTBBT0luQjVXaDRoOUtPSjlIRXNFaVVqYmlvMTkxUm5Uc2hZRktCZVM3Yk1sdi9CTDBOUTRSck1LdHY2Vks0eThoY0lQdU1xcDEwLzJINzBoQ2JrbTRHVUVzTkhtaE1ZQjJUZlBFbmRCQ2tOZ0ZoNlFiVHZ5az0iLCJtYWMiOiIwY2EwZmVjNTAzYTBjNTY4NTIzYjcxNjA0OTc5YWYzMjk1OWM3YzY4NGY3NzRiNmQzNDkyMDU3NjI4ZWE2YTMzIiwidGFnIjoiIn0%3D; ttcsid=1753897739151::jrcBqMKvIJe7sED08Eew.2.1753897825781; ttcsid_CM0OVARC77UDNKHAHMLG=1753897739150::o8q2bNW2cww8z-WM1DwD.2.1753897838814; _ga_WDSYD0V4EF=GS2.1.s1753897738$o2$g1$t1753897850$j34$l0$h557063752; XSRF-TOKEN=eyJpdiI6InR0ZHlFclhSdXlkcVpzOXVvSTVJYVE9PSIsInZhbHVlIjoiYnZEazdiV0d0V0E1U3QrQzAwcUsvbkFuMTAycW9TUEdxNGZiTWE1ckFJeTdQS3VOTjFVRU9MRGdGc3BFTENrZVAwNXlHaTZnS2J4VUg2OEJXM2RWeDdzNjhISC9MRWFVZyt5YXovZ1RYdUEreFlJQnBZVTk2NDE5UlkxY2Vjc0YiLCJtYWMiOiJmZDA4YmNmOTAzNWY1YTk4MzM1NTEzZjE1ZWNiZTFhZWZjMTVhZTQyODgzNjA4ZTE4ODMxOTUxMTQ4NzBmODAxIiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6InJQRkl4QXY3RlQvYXFBb3hFTkJFdnc9PSIsInZhbHVlIjoiSEtLSGRzV1ZhdmV2NG03M3grT0ZISlFVQ0JpSXM2ZVl4RU1hNlhzU05DMVVjTkxEbkxMbGZxTWs2cnZuN3p2ZjI0dG9QMW0xZmYrYXlYcVJpc2o1d2dzSVNLSEpTRjFHWFFCbUthZzJSNkNmYlJpbEpjaTRoZWFKSHYzN05icHQiLCJtYWMiOiI0NmM4NmYwY2U1ZjQ0M2VmMjE5Y2NiNGIyZjdjNmE0OGY2YjM3YTdmYWJhZTM1MWU4YjQ5NDNlZjRlZTdiNjY5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1352765783\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1189651508 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">31bb862467c8e69b344d5c45026a75ed73ed5d26</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"351 characters\">{&quot;footprint&quot;:&quot;31bb862467c8e69b344d5c45026a75ed73ed5d26&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;chubbybyp-v2.gc&quot;,&quot;landing_page&quot;:&quot;\\/&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:&quot;http:\\/\\/localhost\\/&quot;,&quot;referrer_domain&quot;:&quot;localhost&quot;}</span>\"\n  \"<span class=sf-dump-key>_gcl_au</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_tt_enable_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ttp</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|AGlUzxciifw9JcFUPAL5OSaSqWH54hoxjvrpteKTZIiKkGVxMRd4jFgyKOSx|$2y$12$ou/bc46LY/TX4Ep5.Uq6GOtqAMuOXT6Ej7RDW9moFmk/a0wvYTZJa</span>\"\n  \"<span class=sf-dump-key>ttcsid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>ttcsid_CM0OVARC77UDNKHAHMLG</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_WDSYD0V4EF</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jh1lEMPqUbufsTpHwVjiRRuAGAYe8v5WBn4lkaI4</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JupNiy8yjG2B1llXoLp4Hx796gEZWY8Hc5s0vzYH</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1189651508\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1844899377 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 17:51:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"436 characters\">XSRF-TOKEN=eyJpdiI6ImcvT1FWNVU5UmhWekZaNGtCWEJ0Mmc9PSIsInZhbHVlIjoiTm5SWTRIL2tkYUg1NVVVTitzUUZBb1F5MTdVMldERmNTWmNzcFAvcUFvcGNyNXRkeUF1NCtTZ2hRczEwVGZUK1h4Ryt2UWIwRzltNzFGaEZFdmx5QWs2VTM4MkhYd3lRYjFiVlk5TXFDdldEOW5jZzJ4REFpS2Z2M1pQd1d5MDUiLCJtYWMiOiI2NjgwZmZiNzJhNmM3Zjk2MTE1ZWJjYWIxOGIxMzEyYjAxZDg1ODFhY2I3ZDhlZWU5NTMyNWE2ODMwNGRjOTI3IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 19:51:54 GMT; Max-Age=7200; path=/; secure; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"442 characters\">botble_session=eyJpdiI6IlNsUVRpZFVKaG1WQU56RUswT2trY2c9PSIsInZhbHVlIjoielR0ME81dEcwblBDNXlWaFBEbmZ2MUlqVVM1ZHBILzUxSkV6MlpVUTlpQXdmaktZQ3JyS0pvWTFQcDRBRnh5SnFxMFVhSTd2ZjZqaHcraFpneGNtVVZHOW94SENmTUphODBGa0Q5LzJYOU1oWTZId0lPVFdvUk5wUFMyUlJPMVgiLCJtYWMiOiI4YTM0NDFiMTc3MDZjMDdlYjVmNWJlMjZjZTc4MGNmMmQ4MTdkNDE3YWU0MGEzMDI4ODcxNDBkMjg1YThjNmU2IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 19:51:54 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"408 characters\">XSRF-TOKEN=eyJpdiI6ImcvT1FWNVU5UmhWekZaNGtCWEJ0Mmc9PSIsInZhbHVlIjoiTm5SWTRIL2tkYUg1NVVVTitzUUZBb1F5MTdVMldERmNTWmNzcFAvcUFvcGNyNXRkeUF1NCtTZ2hRczEwVGZUK1h4Ryt2UWIwRzltNzFGaEZFdmx5QWs2VTM4MkhYd3lRYjFiVlk5TXFDdldEOW5jZzJ4REFpS2Z2M1pQd1d5MDUiLCJtYWMiOiI2NjgwZmZiNzJhNmM3Zjk2MTE1ZWJjYWIxOGIxMzEyYjAxZDg1ODFhY2I3ZDhlZWU5NTMyNWE2ODMwNGRjOTI3IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 19:51:54 GMT; path=/; secure</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"414 characters\">botble_session=eyJpdiI6IlNsUVRpZFVKaG1WQU56RUswT2trY2c9PSIsInZhbHVlIjoielR0ME81dEcwblBDNXlWaFBEbmZ2MUlqVVM1ZHBILzUxSkV6MlpVUTlpQXdmaktZQ3JyS0pvWTFQcDRBRnh5SnFxMFVhSTd2ZjZqaHcraFpneGNtVVZHOW94SENmTUphODBGa0Q5LzJYOU1oWTZId0lPVFdvUk5wUFMyUlJPMVgiLCJtYWMiOiI4YTM0NDFiMTc3MDZjMDdlYjVmNWJlMjZjZTc4MGNmMmQ4MTdkNDE3YWU0MGEzMDI4ODcxNDBkMjg1YThjNmU2IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 19:51:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1844899377\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-302916446 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jh1lEMPqUbufsTpHwVjiRRuAGAYe8v5WBn4lkaI4</span>\"\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"65 characters\">https://chubbybyp-v2.gc/checkout/241afa5f1f6e9ca99f6cbf0bf30e3abd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>cart</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>cart_updated_at</span>\" => <span class=sf-dump-note title=\"Carbon\\Carbon @1753897882\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Carbon @1753897882</span> {<a class=sf-dump-ref>#3663</a><samp data-depth=3 class=sf-dump-compact>\n      #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n      #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n      #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">0000000000000e4f0000000000000000</span>\"\n      #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n        <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n      </samp>]\n      #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n      <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Wednesday, July 30, 2025\n- 00:00:32.424502 from now\nDST Off\">2025-07-30 21:51:22.176601 Asia/Dubai (+04:00)</span>\n    </samp>}\n    \"<span class=sf-dump-key>cart</span>\" => <span class=sf-dump-note title=\"Illuminate\\Support\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Collection</span> {<a class=sf-dump-ref>#3664</a><samp data-depth=3 class=sf-dump-compact>\n      #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>0ef574d36ebdc3a49943f0917f4f1fdb</span>\" => <span class=sf-dump-note title=\"Botble\\Ecommerce\\Cart\\CartItem\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Ecommerce\\Cart</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>CartItem</span> {<a class=sf-dump-ref>#3665</a><samp data-depth=5 class=sf-dump-compact>\n          +<span class=sf-dump-public title=\"Public property\">rowId</span>: \"<span class=sf-dump-str title=\"32 characters\">0ef574d36ebdc3a49943f0917f4f1fdb</span>\"\n          +<span class=sf-dump-public title=\"Public property\">id</span>: <span class=sf-dump-num>844</span>\n          +<span class=sf-dump-public title=\"Public property\">qty</span>: \"<span class=sf-dump-str>1</span>\"\n          +<span class=sf-dump-public title=\"Public property\">name</span>: \"<span class=sf-dump-str title=\"30 characters\">Plus Size No Sleeve Ruffle Top</span>\"\n          +<span class=sf-dump-public title=\"Public property\">price</span>: <span class=sf-dump-num>45.0</span>\n          +<span class=sf-dump-public title=\"Public property\">options</span>: <span class=sf-dump-note title=\"Botble\\Ecommerce\\Cart\\CartItemOptions\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Ecommerce\\Cart</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>CartItemOptions</span> {<a class=sf-dump-ref>#3666</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"53 characters\">products/CHUBBY_BY_-_WBG-111__1_-removebg-preview.png</span>\"\n              \"<span class=sf-dump-key>attributes</span>\" => \"<span class=sf-dump-str title=\"28 characters\">(Color: Black, Size: XXL/44)</span>\"\n              \"<span class=sf-dump-key>taxRate</span>\" => <span class=sf-dump-num>0.0</span>\n              \"<span class=sf-dump-key>taxClasses</span>\" => []\n              \"<span class=sf-dump-key>options</span>\" => []\n              \"<span class=sf-dump-key>extras</span>\" => []\n              \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"5 characters\">00180</span>\"\n              \"<span class=sf-dump-key>weight</span>\" => <span class=sf-dump-num>0.0</span>\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          </samp>}\n          #<span class=sf-dump-protected title=\"Protected property\">associatedModel</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">taxRate</span>: <span class=sf-dump-num>0.0</span>\n          +\"<span class=sf-dump-public title=\"Runtime added dynamic property\">created_at</span>\": <span class=sf-dump-note title=\"Carbon\\Carbon @1753897882\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Carbon @1753897882</span> {<a class=sf-dump-ref>#3672</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">0000000000000e580000000000000000</span>\"\n            #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n            <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Wednesday, July 30, 2025\n- 00:00:32.425187 from now\nDST Off\">2025-07-30 21:51:22.176110 Asia/Dubai (+04:00)</span>\n          </samp>}\n          +\"<span class=sf-dump-public title=\"Runtime added dynamic property\">updated_at</span>\": <span class=sf-dump-note title=\"Carbon\\Carbon @1753897882\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Carbon @1753897882</span> {<a class=sf-dump-ref>#3674</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">0000000000000e5a0000000000000000</span>\"\n            #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n            <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Wednesday, July 30, 2025\n- 00:00:32.424954 from now\nDST Off\">2025-07-30 21:51:22.176431 Asia/Dubai (+04:00)</span>\n          </samp>}\n        </samp>}\n      </samp>]\n      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n    </samp>}\n  </samp>]\n  \"<span class=sf-dump-key>tracked_start_checkout</span>\" => \"<span class=sf-dump-str title=\"32 characters\">241afa5f1f6e9ca99f6cbf0bf30e3abd</span>\"\n  \"<span class=sf-dump-key>d0048421b8a0e3557bda5c3fde284d0e</span>\" => <span class=sf-dump-note>array:15</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>promotion_discount_amount</span>\" => <span class=sf-dump-num>0</span>\n    \"<span class=sf-dump-key>billing_address_same_as_shipping_address</span>\" => <span class=sf-dump-const>true</span>\n    \"<span class=sf-dump-key>billing_address</span>\" => []\n    \"<span class=sf-dump-key>created_order</span>\" => <span class=sf-dump-const>true</span>\n    \"<span class=sf-dump-key>created_order_id</span>\" => <span class=sf-dump-num>179</span>\n    \"<span class=sf-dump-key>is_save_order_shipping_address</span>\" => <span class=sf-dump-const>true</span>\n    \"<span class=sf-dump-key>created_order_product</span>\" => <span class=sf-dump-note title=\"Carbon\\Carbon @1753897842\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Carbon @1753897842</span> {<a class=sf-dump-ref>#3676</a><samp data-depth=3 class=sf-dump-compact>\n      #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n      #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n      #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">0000000000000e5c0000000000000000</span>\"\n      #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n        <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n      </samp>]\n      #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n      <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Wednesday, July 30, 2025\n- 00:01:12.304693 from now\nDST Off\">2025-07-30 21:50:42.296475 Asia/Dubai (+04:00)</span>\n    </samp>}\n    \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>email</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>phone</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>address</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>zip_code</span>\" => \"<span class=sf-dump-str title=\"5 characters\">admin</span>\"\n    \"<span class=sf-dump-key>country</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>state</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>city</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>selected_payment_method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">cod</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-302916446\", {\"maxDepth\":0})</script>\n"}}