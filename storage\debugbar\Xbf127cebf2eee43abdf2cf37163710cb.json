{"__meta": {"id": "Xbf127cebf2eee43abdf2cf37163710cb", "datetime": "2025-07-30 21:50:57", "utime": **********.935795, "method": "GET", "uri": "/ajax/states-by-country?country_id=38", "ip": "127.0.0.1"}, "php": {"version": "8.3.17", "interface": "apache2handler"}, "messages": {"count": 2, "messages": [{"message": "[21:50:57] LOG.warning: Creation of dynamic property Botble\\Ecommerce\\Cart\\CartItem::$created_at is deprecated in D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php on line 117", "message_html": null, "is_string": false, "label": "warning", "time": **********.86605, "xdebug_link": null, "collector": "log"}, {"message": "[21:50:57] LOG.warning: Creation of dynamic property Botble\\Ecommerce\\Cart\\CartItem::$updated_at is deprecated in D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php on line 117", "message_html": null, "is_string": false, "label": "warning", "time": **********.866926, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753897856.760165, "end": **********.935829, "duration": 1.175663948059082, "duration_str": "1.18s", "measures": [{"label": "Booting", "start": 1753897856.760165, "relative_start": 0, "end": **********.805551, "relative_end": **********.805551, "duration": 1.0453860759735107, "duration_str": "1.05s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.805573, "relative_start": 1.045408010482788, "end": **********.935831, "relative_end": 2.1457672119140625e-06, "duration": 0.13025808334350586, "duration_str": "130ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43352448, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET ajax/states-by-country", "middleware": "web, core, localeSessionRedirect, localizationRedirect", "controller": "Botble\\Location\\Http\\Controllers\\StateController@ajaxGetStates", "namespace": "Botble\\Location\\Http\\Controllers", "prefix": "/", "where": [], "as": "ajax.states-by-country", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Flocation%2Fsrc%2FHttp%2FControllers%2FStateController.php&line=98\" onclick=\"\">platform/plugins/location/src/Http/Controllers/StateController.php:98-119</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.017179999999999997, "accumulated_duration_str": "17.18ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Footprints/TrackingFilter.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\ecommerce\\src\\Services\\Footprints\\TrackingFilter.php", "line": 45}], "start": **********.883168, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:55", "source": "platform/core/base/src/Models/BaseQueryBuilder.php:55", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=55", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "55"}, "connection": "chubbybyp-v2", "start_percent": 0, "width_percent": 3.492}, {"sql": "select `id`, `name` from `states` where `status` = 'published' and `country_id` = '38' order by `order` asc, `name` asc", "type": "query", "params": [], "bindings": ["published", "38"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 55}, {"index": 16, "namespace": null, "name": "platform/plugins/location/src/Http/Controllers/StateController.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\platform\\plugins\\location\\src\\Http\\Controllers\\StateController.php", "line": 112}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\chubbybyp-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.888371, "duration": 0.016579999999999998, "duration_str": "16.58ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:55", "source": "platform/core/base/src/Models/BaseQueryBuilder.php:55", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=55", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "55"}, "connection": "chubbybyp-v2", "start_percent": 3.492, "width_percent": 96.508}]}, "models": {"data": {"Botble\\Location\\Models\\State<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fplugins%2Flocation%2Fsrc%2FModels%2FState.php&line=1\" class=\"phpdebugbar-widgets-editor-link\"></a>": 13, "Botble\\ACL\\Models\\User<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fchubbybyp-v2%2Fplatform%2Fcore%2Facl%2Fsrc%2FModels%2FUser.php&line=1\" class=\"phpdebugbar-widgets-editor-link\"></a>": 1}, "count": 14, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "jh1lEMPqUbufsTpHwVjiRRuAGAYe8v5WBn4lkaI4", "language": "en", "_previous": "array:1 [\n  \"url\" => \"https://chubbybyp-v2.gc/checkout/241afa5f1f6e9ca99f6cbf0bf30e3abd\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "cart": "array:2 [\n  \"cart_updated_at\" => Carbon\\Carbon @1753897842 {#3567\n    #endOfTime: false\n    #startOfTime: false\n    #constructedObjectId: \"0000000000000def0000000000000000\"\n    #localMonthsOverflow: null\n    #localYearsOverflow: null\n    #localStrictModeEnabled: null\n    #localHumanDiffOptions: null\n    #localToStringFormat: null\n    #localSerializer: null\n    #localMacros: null\n    #localGenericMacros: null\n    #localFormatFunction: null\n    #localTranslator: null\n    #dumpProperties: array:3 [\n      0 => \"date\"\n      1 => \"timezone_type\"\n      2 => \"timezone\"\n    ]\n    #dumpLocale: null\n    #dumpDateProperties: null\n    date: 2025-07-30 21:50:42.296475 Asia/Dubai (+04:00)\n  }\n  \"cart\" => Illuminate\\Support\\Collection {#3568\n    #items: array:1 [\n      \"0ef574d36ebdc3a49943f0917f4f1fdb\" => Botble\\Ecommerce\\Cart\\CartItem {#3569\n        +rowId: \"0ef574d36ebdc3a49943f0917f4f1fdb\"\n        +id: 844\n        +qty: \"1\"\n        +name: \"Plus Size No Sleeve Ruffle Top\"\n        +price: 45.0\n        +options: Botble\\Ecommerce\\Cart\\CartItemOptions {#3570\n          #items: array:8 [\n            \"image\" => \"products/CHUBBY_BY_-_WBG-111__1_-removebg-preview.png\"\n            \"attributes\" => \"(Color: Black, Size: XXL/44)\"\n            \"taxRate\" => 0.0\n            \"taxClasses\" => []\n            \"options\" => []\n            \"extras\" => []\n            \"sku\" => \"00180\"\n            \"weight\" => 0.0\n          ]\n          #escapeWhenCastingToString: false\n        }\n        #associatedModel: null\n        #taxRate: 0.0\n        +\"created_at\": Carbon\\Carbon @1753897842 {#3576\n          #endOfTime: false\n          #startOfTime: false\n          #constructedObjectId: \"0000000000000df80000000000000000\"\n          #localMonthsOverflow: null\n          #localYearsOverflow: null\n          #localStrictModeEnabled: null\n          #localHumanDiffOptions: null\n          #localToStringFormat: null\n          #localSerializer: null\n          #localMacros: null\n          #localGenericMacros: null\n          #localFormatFunction: null\n          #localTranslator: null\n          #dumpProperties: array:3 [\n            0 => \"date\"\n            1 => \"timezone_type\"\n            2 => \"timezone\"\n          ]\n          #dumpLocale: null\n          #dumpDateProperties: null\n          date: 2025-07-30 21:50:42.295907 Asia/Dubai (+04:00)\n        }\n        +\"updated_at\": Carbon\\Carbon @1753897842 {#3578\n          #endOfTime: false\n          #startOfTime: false\n          #constructedObjectId: \"0000000000000dfa0000000000000000\"\n          #localMonthsOverflow: null\n          #localYearsOverflow: null\n          #localStrictModeEnabled: null\n          #localHumanDiffOptions: null\n          #localToStringFormat: null\n          #localSerializer: null\n          #localMacros: null\n          #localGenericMacros: null\n          #localFormatFunction: null\n          #localTranslator: null\n          #dumpProperties: array:3 [\n            0 => \"date\"\n            1 => \"timezone_type\"\n            2 => \"timezone\"\n          ]\n          #dumpLocale: null\n          #dumpDateProperties: null\n          date: 2025-07-30 21:50:42.296221 Asia/Dubai (+04:00)\n        }\n      }\n    ]\n    #escapeWhenCastingToString: false\n  }\n]", "tracked_start_checkout": "241afa5f1f6e9ca99f6cbf0bf30e3abd", "d0048421b8a0e3557bda5c3fde284d0e": "array:7 [\n  \"promotion_discount_amount\" => 0\n  \"billing_address_same_as_shipping_address\" => true\n  \"billing_address\" => []\n  \"created_order\" => true\n  \"created_order_id\" => 179\n  \"is_save_order_shipping_address\" => true\n  \"created_order_product\" => Carbon\\Carbon @1753897842 {#3567\n    #endOfTime: false\n    #startOfTime: false\n    #constructedObjectId: \"0000000000000def0000000000000000\"\n    #localMonthsOverflow: null\n    #localYearsOverflow: null\n    #localStrictModeEnabled: null\n    #localHumanDiffOptions: null\n    #localToStringFormat: null\n    #localSerializer: null\n    #localMacros: null\n    #localGenericMacros: null\n    #localFormatFunction: null\n    #localTranslator: null\n    #dumpProperties: array:3 [\n      0 => \"date\"\n      1 => \"timezone_type\"\n      2 => \"timezone\"\n    ]\n    #dumpLocale: null\n    #dumpDateProperties: null\n    date: 2025-07-30 21:50:42.296475 Asia/Dubai (+04:00)\n  }\n]", "selected_payment_method": "paymob"}, "request": {"path_info": "/ajax/states-by-country", "status_code": "<pre class=sf-dump id=sf-dump-1624070519 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1624070519\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1754611254 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>country_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">38</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1754611254\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1800437471 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1800437471\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-211666916 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">chubbybyp-v2.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">https://chubbybyp-v2.gc/checkout/241afa5f1f6e9ca99f6cbf0bf30e3abd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2880 characters\">botble_footprints_cookie=eyJpdiI6IkZkR1A0Y29ydU9wSkpvdi9MdVk3UHc9PSIsInZhbHVlIjoiZE1HR0ZoQjdmTzV5OHNtaUFkaWNDR1l4OERXYldBWCs1YURGeXV4SmpBaldKSWJiT0NJVURReDlyNktldG0vRy8yN0NocFBqNmRBMXh4dVZRQVdyekVEQjdqRmlvSksxNGNVVGkzTmFZMEFXR3o4Mm96UXdtWTZ0YldpOS94UGoiLCJtYWMiOiJkMjdiZWUwMDlkNTVmYTIxYjM1NGQwYmMxZTY1ODg1ODgwODRhZjFkZGUzMGIzYjYyOWFmYTVlMDMyZTIyYzkxIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6Ik1oMEM3S3NpZWprSTY5RTVGOFFaQ3c9PSIsInZhbHVlIjoiNjc1eE1LTGZpaGsraVZ3QW13Q3JXaWhySXRUMkRDNERld0k3VzFwMFNheklvOEI5dkZFWjF3RDdMODFxVmttWE9UajJTOHl1eWlHTGV5eFVHYTYxaU5jZ3dQbVdaRlpXRDNZMGxYL242RVhQdDNTbmFsMVJ2RWgxdVN6aHdLS2xvQUV0U1YvNHF6a1lIL0FRa1ZMVEdnWEpHd3hUS1dvVURMOWIwWWRBTTNSbUVmRlVSY0VYUHVqOTg3RThJNXpHekpHZmtEK1Z1SWJhRUJpODZEQ1J0MzBoQmJ4ekZzRnJPdWM4aDZuM3ZZNjk4NkpJVFI4WGpVMnV5TTdTM3RVY3ZGTkUrWWJWTDRLZjVhODl5NW5iUFQ3dGlJRkVFUy9qcTJmK0YzS0MwZnRjbmxhOXFKWCtnazNzZXhFNHBNckZ4VXR2S0JsUjhnbTRqQ1R3OEd1RjRqMEp3cGExeU1Wa1k5WVp5SlB6ditjeGZjSFZBMyt0dFZZZFcya095b2pmM0FMWm5Yb0ZaMzM5cWE4YzlNV285RmVxTVg5TlBiYllxTkNBeGZ2MnUvQlBLY1h5a3ZJWi9nY1B1b1hUZWlMeWRXOG1HclJZVnk5cGdzMXp5N3FSTGE1SzV5Z1k5RS9CM1VwU2JKakt0czJuRklyODdPWUpiblovdWxMK045ZGJUd2FNVERGWmlnVmUxNldKZTZMSEtnPT0iLCJtYWMiOiJiYjUwN2IwMDY4OTJjY2JlNzQ1YWUwODIxYTVhMmUzZjBlYzQyZWFmMzQzNmQ4NzUwOGUxNTc4ODcxMjhiZTJhIiwidGFnIjoiIn0%3D; _gcl_au=1.1.1091535652.1753890285; _ga=GA1.1.1700318349.1753890285; _tt_enable_cookie=1; _ttp=01K1E03SZ79B2723EEJDJ4JGVK_.tt.1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im5pZnQ3WnIyeTZLbE42WVNEVGZUcFE9PSIsInZhbHVlIjoiTTllTlo2bGNYbTRod1NYTWJJQms0WVdLcXBWVVVQTjJlWFJUUXNFYzlVTy9qbFROTGdDeVNnRGdyYyswZC9oZkwraVRLcFNSUzUwdk04TWY2MGY3Z1lyL3FYdEx4eDQ4V0NNQUZDQnJ2MG84NVQ3UFpXNmtnWTBBT0luQjVXaDRoOUtPSjlIRXNFaVVqYmlvMTkxUm5Uc2hZRktCZVM3Yk1sdi9CTDBOUTRSck1LdHY2Vks0eThoY0lQdU1xcDEwLzJINzBoQ2JrbTRHVUVzTkhtaE1ZQjJUZlBFbmRCQ2tOZ0ZoNlFiVHZ5az0iLCJtYWMiOiIwY2EwZmVjNTAzYTBjNTY4NTIzYjcxNjA0OTc5YWYzMjk1OWM3YzY4NGY3NzRiNmQzNDkyMDU3NjI4ZWE2YTMzIiwidGFnIjoiIn0%3D; ttcsid=1753897739151::jrcBqMKvIJe7sED08Eew.2.1753897825781; ttcsid_CM0OVARC77UDNKHAHMLG=1753897739150::o8q2bNW2cww8z-WM1DwD.2.1753897838814; XSRF-TOKEN=eyJpdiI6InAwK1pxMTJsb2lJRVpBd2phMU4wUFE9PSIsInZhbHVlIjoiYjkrUUJkN2NWVGwzd1Vac0JMcmR5SU5JMmxJMTlvLytVWVlDK0dOTGRJZG50c0ZTT0dicDJWc21XUk16RUpZWTJTdElTbHJPU2RZbzF5eUhJS3BqUE1uenlwZXdQWm43a0lnR3J3RXNESlkxWjQ3TGQvU3hJL25pTzVmWS8vRXAiLCJtYWMiOiI1NWMzOGYxZjIyMzI0ZGU3ZTdjNzI1OTY2MDYwZGFmZjZmMDRiMGQ4NjRjY2RhNGJlNTllZWY2MjFmMzY3YzU0IiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6ImVmb1FFRHY1T2JVZllJamdLbk5nL2c9PSIsInZhbHVlIjoiN2ZESTlhdi9EV2Jmckx5ZFhDZ2szbTdhaVpnM04xVTdGZ0pZbkVHVHRtQ3ZscG92akN2V1k1V2l6Y0JYOS9JTkN4c01nTmRLOWlERHhzZWpSdGxIVTVwYUZQWTFIODR4d2pRMGYvM0FvU1JsMHU1WjdUN1NiaDJvL2Q5UGpMY3kiLCJtYWMiOiJkMmZkNjE5ZjkxOGE5NjM2ZjhmMWM5YmJiZTdmMWUzNzZjMjE5NTk0Yzc5YjE2NmQwNWEwZTFlMTQyYTIzMmQ2IiwidGFnIjoiIn0%3D; _ga_WDSYD0V4EF=GS2.1.s1753897738$o2$g1$t1753897850$j34$l0$h557063752</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-211666916\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1500814236 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">31bb862467c8e69b344d5c45026a75ed73ed5d26</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"351 characters\">{&quot;footprint&quot;:&quot;31bb862467c8e69b344d5c45026a75ed73ed5d26&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;chubbybyp-v2.gc&quot;,&quot;landing_page&quot;:&quot;\\/&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:&quot;http:\\/\\/localhost\\/&quot;,&quot;referrer_domain&quot;:&quot;localhost&quot;}</span>\"\n  \"<span class=sf-dump-key>_gcl_au</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_tt_enable_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ttp</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|AGlUzxciifw9JcFUPAL5OSaSqWH54hoxjvrpteKTZIiKkGVxMRd4jFgyKOSx|$2y$12$ou/bc46LY/TX4Ep5.Uq6GOtqAMuOXT6Ej7RDW9moFmk/a0wvYTZJa</span>\"\n  \"<span class=sf-dump-key>ttcsid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>ttcsid_CM0OVARC77UDNKHAHMLG</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jh1lEMPqUbufsTpHwVjiRRuAGAYe8v5WBn4lkaI4</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JupNiy8yjG2B1llXoLp4Hx796gEZWY8Hc5s0vzYH</span>\"\n  \"<span class=sf-dump-key>_ga_WDSYD0V4EF</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1500814236\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-542161367 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 17:50:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"436 characters\">XSRF-TOKEN=eyJpdiI6IkRmNzc0WEh4ZUNJVVBINzdhRWhXc2c9PSIsInZhbHVlIjoiZk5iaHQ5UkptNlZTbkZMUlhTWVFpRnI4MU1sZHNpREt5YnVkNDhiaUJlMkNSbGNtaVhUaDNBSEpTK2ZXVEY4NlNIOFpuMHRjdW5kcEFDMk9hbFJKa0lsUWhiUDJIeXVvN0tQdk9ubmlmQnB6NW9oSkhFZTROWGwzUGpZazdRbXYiLCJtYWMiOiIzZTEyMDQyMmM5MTg2MDk2NmJhNDAyZmE0ZjkwMTgxMjAyZDJmMjZjMDk1NDU4ZTAzMjIzMTZiY2IxZjc4NGUxIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 19:50:57 GMT; Max-Age=7200; path=/; secure; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"442 characters\">botble_session=eyJpdiI6InJ1blExNG5QcFFkQjhvSHNzaVZNamc9PSIsInZhbHVlIjoiQlVGN3ZBTDZZN3g5ZGtESDhMNlVGM3FCMTF3UTNIUktVYm4vM2RSaERMSEF4L1JPcEEyRkhlRitTQTBlSElqN1RIVEdMSHVtR2JaSVZwbFBSRkY1MEZJR2dDK3RiSU1qSkZwbUpIdGl2K1I4a1puYTNGVHBVVW9zVDM4b3JsUisiLCJtYWMiOiIxZmU4N2RmOTQ5OGFiNmUxNWViZGQwZDExZDA0N2I2MDkzMzFmMTgzN2RmMzVjYWI2NzAyOWQ1MjM2OTk5MmI2IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 19:50:57 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"408 characters\">XSRF-TOKEN=eyJpdiI6IkRmNzc0WEh4ZUNJVVBINzdhRWhXc2c9PSIsInZhbHVlIjoiZk5iaHQ5UkptNlZTbkZMUlhTWVFpRnI4MU1sZHNpREt5YnVkNDhiaUJlMkNSbGNtaVhUaDNBSEpTK2ZXVEY4NlNIOFpuMHRjdW5kcEFDMk9hbFJKa0lsUWhiUDJIeXVvN0tQdk9ubmlmQnB6NW9oSkhFZTROWGwzUGpZazdRbXYiLCJtYWMiOiIzZTEyMDQyMmM5MTg2MDk2NmJhNDAyZmE0ZjkwMTgxMjAyZDJmMjZjMDk1NDU4ZTAzMjIzMTZiY2IxZjc4NGUxIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 19:50:57 GMT; path=/; secure</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"414 characters\">botble_session=eyJpdiI6InJ1blExNG5QcFFkQjhvSHNzaVZNamc9PSIsInZhbHVlIjoiQlVGN3ZBTDZZN3g5ZGtESDhMNlVGM3FCMTF3UTNIUktVYm4vM2RSaERMSEF4L1JPcEEyRkhlRitTQTBlSElqN1RIVEdMSHVtR2JaSVZwbFBSRkY1MEZJR2dDK3RiSU1qSkZwbUpIdGl2K1I4a1puYTNGVHBVVW9zVDM4b3JsUisiLCJtYWMiOiIxZmU4N2RmOTQ5OGFiNmUxNWViZGQwZDExZDA0N2I2MDkzMzFmMTgzN2RmMzVjYWI2NzAyOWQ1MjM2OTk5MmI2IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 19:50:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-542161367\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-452555425 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jh1lEMPqUbufsTpHwVjiRRuAGAYe8v5WBn4lkaI4</span>\"\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"65 characters\">https://chubbybyp-v2.gc/checkout/241afa5f1f6e9ca99f6cbf0bf30e3abd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>cart</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>cart_updated_at</span>\" => <span class=sf-dump-note title=\"Carbon\\Carbon @1753897842\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Carbon @1753897842</span> {<a class=sf-dump-ref href=#sf-dump-452555425-ref23567 title=\"2 occurrences\">#3567</a><samp data-depth=3 id=sf-dump-452555425-ref23567 class=sf-dump-compact>\n      #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n      #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n      #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">0000000000000def0000000000000000</span>\"\n      #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n        <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n      </samp>]\n      #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n      <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Wednesday, July 30, 2025\n- 00:00:15.647614 from now\nDST Off\">2025-07-30 21:50:42.296475 Asia/Dubai (+04:00)</span>\n    </samp>}\n    \"<span class=sf-dump-key>cart</span>\" => <span class=sf-dump-note title=\"Illuminate\\Support\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Collection</span> {<a class=sf-dump-ref>#3568</a><samp data-depth=3 class=sf-dump-compact>\n      #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>0ef574d36ebdc3a49943f0917f4f1fdb</span>\" => <span class=sf-dump-note title=\"Botble\\Ecommerce\\Cart\\CartItem\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Ecommerce\\Cart</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>CartItem</span> {<a class=sf-dump-ref>#3569</a><samp data-depth=5 class=sf-dump-compact>\n          +<span class=sf-dump-public title=\"Public property\">rowId</span>: \"<span class=sf-dump-str title=\"32 characters\">0ef574d36ebdc3a49943f0917f4f1fdb</span>\"\n          +<span class=sf-dump-public title=\"Public property\">id</span>: <span class=sf-dump-num>844</span>\n          +<span class=sf-dump-public title=\"Public property\">qty</span>: \"<span class=sf-dump-str>1</span>\"\n          +<span class=sf-dump-public title=\"Public property\">name</span>: \"<span class=sf-dump-str title=\"30 characters\">Plus Size No Sleeve Ruffle Top</span>\"\n          +<span class=sf-dump-public title=\"Public property\">price</span>: <span class=sf-dump-num>45.0</span>\n          +<span class=sf-dump-public title=\"Public property\">options</span>: <span class=sf-dump-note title=\"Botble\\Ecommerce\\Cart\\CartItemOptions\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Ecommerce\\Cart</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>CartItemOptions</span> {<a class=sf-dump-ref>#3570</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"53 characters\">products/CHUBBY_BY_-_WBG-111__1_-removebg-preview.png</span>\"\n              \"<span class=sf-dump-key>attributes</span>\" => \"<span class=sf-dump-str title=\"28 characters\">(Color: Black, Size: XXL/44)</span>\"\n              \"<span class=sf-dump-key>taxRate</span>\" => <span class=sf-dump-num>0.0</span>\n              \"<span class=sf-dump-key>taxClasses</span>\" => []\n              \"<span class=sf-dump-key>options</span>\" => []\n              \"<span class=sf-dump-key>extras</span>\" => []\n              \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"5 characters\">00180</span>\"\n              \"<span class=sf-dump-key>weight</span>\" => <span class=sf-dump-num>0.0</span>\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          </samp>}\n          #<span class=sf-dump-protected title=\"Protected property\">associatedModel</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">taxRate</span>: <span class=sf-dump-num>0.0</span>\n          +\"<span class=sf-dump-public title=\"Runtime added dynamic property\">created_at</span>\": <span class=sf-dump-note title=\"Carbon\\Carbon @1753897842\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Carbon @1753897842</span> {<a class=sf-dump-ref>#3576</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">0000000000000df80000000000000000</span>\"\n            #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n            <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Wednesday, July 30, 2025\n- 00:00:15.648325 from now\nDST Off\">2025-07-30 21:50:42.295907 Asia/Dubai (+04:00)</span>\n          </samp>}\n          +\"<span class=sf-dump-public title=\"Runtime added dynamic property\">updated_at</span>\": <span class=sf-dump-note title=\"Carbon\\Carbon @1753897842\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Carbon @1753897842</span> {<a class=sf-dump-ref>#3578</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">0000000000000dfa0000000000000000</span>\"\n            #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n            <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Wednesday, July 30, 2025\n- 00:00:15.648066 from now\nDST Off\">2025-07-30 21:50:42.296221 Asia/Dubai (+04:00)</span>\n          </samp>}\n        </samp>}\n      </samp>]\n      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n    </samp>}\n  </samp>]\n  \"<span class=sf-dump-key>tracked_start_checkout</span>\" => \"<span class=sf-dump-str title=\"32 characters\">241afa5f1f6e9ca99f6cbf0bf30e3abd</span>\"\n  \"<span class=sf-dump-key>d0048421b8a0e3557bda5c3fde284d0e</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>promotion_discount_amount</span>\" => <span class=sf-dump-num>0</span>\n    \"<span class=sf-dump-key>billing_address_same_as_shipping_address</span>\" => <span class=sf-dump-const>true</span>\n    \"<span class=sf-dump-key>billing_address</span>\" => []\n    \"<span class=sf-dump-key>created_order</span>\" => <span class=sf-dump-const>true</span>\n    \"<span class=sf-dump-key>created_order_id</span>\" => <span class=sf-dump-num>179</span>\n    \"<span class=sf-dump-key>is_save_order_shipping_address</span>\" => <span class=sf-dump-const>true</span>\n    \"<span class=sf-dump-key>created_order_product</span>\" => <span class=sf-dump-note title=\"Carbon\\Carbon @1753897842\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Carbon @1753897842</span> {<a class=sf-dump-ref href=#sf-dump-452555425-ref23567 title=\"2 occurrences\">#3567</a>}\n  </samp>]\n  \"<span class=sf-dump-key>selected_payment_method</span>\" => \"<span class=sf-dump-str title=\"6 characters\">paymob</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-452555425\", {\"maxDepth\":0})</script>\n"}}