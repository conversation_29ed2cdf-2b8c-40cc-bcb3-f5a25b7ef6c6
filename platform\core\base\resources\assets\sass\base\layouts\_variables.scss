// Header
$header-height: 50px !default; // header height on table and desktop view. default: 42px
$header-height-xs: ($header-height * 2) !default; // header height on mobile view

$menu-toggler-size: 19px;

$header-logo-height: 14px;
$header-menu-toggler-width: $menu-toggler-size;
$header-menu-toggler-height: $menu-toggler-size;
$header-sidebar-toggler-margin: ($header-height - $menu-toggler-size) * 0.5 0 0 0 !default;
$header-responsive-toggler-margin: ($header-height - $menu-toggler-size) * 0.5 6px 0 6px !default;
$header-logo-margin: ($header-height - $header-logo-height) * 0.5 0 0 0 !default; // adjust logo's margin top. default: -1px

$header-top-menu-general-item-padding: 19px 10px 10px 10px !default; // adjust top menu general elements padding. default: 15px 10px 7px 10px
$header-top-menu-general-item-padding-on-mobile: 19px 6px 10px 6px !default; // adjust top menu general elements padding mobile. default: 15px 10px 7px 10px
$header-top-menu-user-item-padding: 16px 6px 13px 8px !default; // adjust top menu user bar element padding. default: 6px 0px 6px 6px
$header-top-menu-user-item-padding-on-mobile: 16px 0px 13px 2px !default; // adjust top menu user bar element padding on mobile. default: 6px 0px 6px 4px
$header-top-menu-language-item-padding: 16px 3px 13px 7px !default; // adjust top menu language bar element padding. default: 11px 1px 11px 5px
$header-top-menu-language-item-padding-on-mobile: 16px 4px 13px 2px !default; // adjust top menu language bar element padding. default: 11px 0px 11px 4px
$header-top-menu-general-item-badge-top: 10px !default; // adjust top menu general elements badge top position. default: 8px
$header-top-menu-icon-font-size: 13px !default;

$header-top-menu-user-font-size: 13px !default;
$header-top-menu-user-font-weight: 300 !default;
$header-top-menu-user-dropdown-link-font-size: 14px !default;
$header-top-menu-user-dropdown-link-font-weight: 300 !default;

// Sidebar
$sidebar-width: 235px !default; // sidebar width. default:235px
$sidebar-collapsed-width: 45px !default; // minimized/collapsed sidebar width. default:35px
$sidebar-collapsed-submenu-width-on-hover: 210px !default; // sub menu width displayed on hover in collapsed sidebar mode. default:210px
$sidebar-logo-container-width: $sidebar-width !default; // logo container width. Normally same with sidebar-width.
$sidebar-logo-container-width-xs: 110px !default; // logo container width in mobile portrait view. default:110px.
$sidebar-logo-container-width-xxs: 100px !default; // logo container width in mobile landscape view. default:100px.

$sidebar-menu-head-font-size: 14px !default;
$sidebar-menu-link-font-size: 14px !default;
$sidebar-menu-link-font-weight: 300 !default;
$sidebar-menu-link-icon-font-size: 16px !default;
$sidebar-menu-sub-menu-link-icon-font-size: 14px !default;

// Page content
$page-content-min-height: 600px !default; // default page content's min height. default:600px.

// Page footer
$page-footer-height: 33px !default;

// Z-index master list
$zindex-header-fixed: 9995 !default;
$zindex-header-static: 9995 !default;
$zindex-quick-sidebar: 10500 !default;
$zindex-quick-sidebar-full-height: 9996 !default;
$zindex-sidebar-fixed: 10000 !default;
$zindex-footer-fixed: 10000 !default;
$zindex-sidebar-submenu: 2000 !default;
$zindex-go-to-top: 10001 !default;

$general-img-path: '/vendor/core/core/base/images/';
$general-font-path: '/vendor/core/core/base/fonts/';
